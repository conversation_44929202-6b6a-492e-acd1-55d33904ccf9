<?php

namespace App\Http\Controllers;

use App\Models\Recipe;

class RecipeController extends Controller
{
    public function index()
    {
        $recipes = Recipe::where('is_published', true)
                         ->latest('published_at')
                         ->paginate(9);

        return view('recipes.index', [
            'recipes' => $recipes,
        ]);
    }

    public function show($slug)
    {
        $recipe = Recipe::with(['category', 'author', 'ingredients.category'])
            ->where('slug', $slug)
            ->where('is_published', true)
            ->firstOrFail();

        $ingredientGroups = collect();
        $toppingGroups = collect();

        foreach ($recipe->ingredients as $ingredient) {
            $category = $ingredient->category;
            if (!$category) continue;

            $type = $category->type;
            $subType = $category->sub_type;

            if ($type === 'ingredient') {
                $ingredientGroups->push([
                    'sub_type' => $subType,
                    'ingredient' => $ingredient
                ]);
            } elseif ($type === 'topping') {
                $toppingGroups->push([
                    'sub_type' => $subType,
                    'ingredient' => $ingredient
                ]);
            }
        }

        $groupedIngredients = $ingredientGroups->groupBy('sub_type')->sortKeys();
        $groupedToppings = $toppingGroups->groupBy('sub_type')->sortKeys();

        return view('recipes.recipes-slug.index', compact('recipe', 'groupedIngredients', 'groupedToppings'));
    }
}