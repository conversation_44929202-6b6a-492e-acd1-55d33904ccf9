<?php

namespace App\Filament\Resources\IngredientCategoryResource\Pages;

use App\Filament\Resources\IngredientCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListIngredientCategories extends ListRecords
{
    // V3 FIX: Use the Translatable trait for this page type
    use ListRecords\Concerns\Translatable;

    protected static string $resource = IngredientCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // V3 FIX: Add the LocaleSwitcher Action
            Actions\LocaleSwitcher::make(),
            Actions\CreateAction::make(),
        ];
    }
}