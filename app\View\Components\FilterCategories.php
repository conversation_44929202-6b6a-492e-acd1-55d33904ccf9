<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\View\Component;

class FilterCategories extends Component
{
    public $categories;

    /**
     * Create a new component instance.
     *
     * @param \Illuminate\Database\Eloquent\Collection $categories
     * @return void
     */
    public function __construct(Collection $categories)
    {
        $this->categories = $categories;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.filter-categories');
    }
}
