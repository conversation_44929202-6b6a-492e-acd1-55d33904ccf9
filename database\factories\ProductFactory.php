<?php

namespace Database\Factories;

use App\Models\ProductCategory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $flavors = [
            ['en' => 'Chocolate', 'id' => 'Coklat'],
            ['en' => 'Strawberry', 'id' => 'Stroberi'],
            ['en' => 'Vanilla', 'id' => 'Vanila'],
            ['en' => 'Original', 'id' => 'Original'],
            ['en' => 'Soft Butter', 'id' => 'Mentega Lembut'],
        ];

        $colors = [
            ['en' => 'Red', 'id' => 'Merah'],
            ['en' => 'Blue', 'id' => 'Biru'],
            ['en' => 'Green', 'id' => 'Hijau'],
            ['en' => 'Yellow', 'id' => 'Kuning'],
            ['en' => 'Brown', 'id' => 'Coklat'],
            ['en' => 'White', 'id' => 'Putih'],
        ];

        $randomFlavor = fake()->randomElement($flavors);
        $randomColor = fake()->randomElement($colors);

        $englishName = fake()->unique()->words(3, true);

        return [
            'name' => [
                'en' => $englishName,
                'id' => fake('id_ID')->unique()->words(3, true),
            ],
            'slug' => Str::slug($englishName),
            'description' => [
                'en' => fake()->paragraph(3),
                'id' => fake('id_ID')->paragraph(3),
            ],

            'flavor' => [
                'en' => $randomFlavor['en'],
                'id' => $randomFlavor['id'],
            ],
            'color' => [
                'en' => $randomColor['en'],
                'id' => $randomColor['id'],
            ],

            'brand' => 'Moremade',
            'pack_size' => fake()->randomElement(['250g', '500g', '1kg', 'Sachet']),
            'image_url' => fake()->imageUrl(640, 480, 'food', true),
            'product_code' => fake()->unique()->ean8(),
            'is_featured' => fake()->boolean(50),
            'category_id' => ProductCategory::inRandomOrder()->first()->id,
        ];
    }
}
