<?php
namespace App\Filament\Widgets;

use App\Models\Article;
use App\Models\Product;
use App\Models\Recipe;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
class RecipeStats extends BaseWidget
{
    protected static bool $isLazy = true;
    protected static ?int $sort = 3;

    protected function getColumns(): int
    {
        return 2;
    }

    protected function getStats(): array
    {
        return [
            Stat::make('Total Recipes', Recipe::count()),
            Stat::make('Total Recipes Published', Recipe::where('is_published', true)->count()),
        ];
    }
}