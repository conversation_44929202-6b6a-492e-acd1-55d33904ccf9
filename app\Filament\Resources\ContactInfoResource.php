<?php
namespace App\Filament\Resources;
use App\Filament\Resources\ContactInfoResource\Pages;
use App\Models\ContactInfo;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ContactInfoResource extends Resource {
    protected static ?string $model = ContactInfo::class;
    protected static ?string $navigationIcon = 'heroicon-o-identification';
    protected static ?string $navigationGroup = 'Settings';

    public static function form(Form $form): Form {
        return $form->schema([
            Forms\Components\TextInput::make('address'),
            Forms\Components\TextInput::make('phone_number'),
            Forms\Components\TextInput::make('email')->email(),
            Forms\Components\TextInput::make('operational_hours'),
            Forms\Components\Textarea::make('map_embed_code')->columnSpanFull(),
        ]);
    }

    public static function table(Table $table): Table {
        return $table->columns([
            Tables\Columns\TextColumn::make('email'),
            Tables\Columns\TextColumn::make('phone_number'),
            Tables\Columns\TextColumn::make('updated_at')->dateTime(),
        ])->actions([Tables\Actions\EditAction::make()])
          ->bulkActions([]);
    }
    public static function getPages(): array { return ['index' => Pages\ListContactInfos::route('/'), 'create' => Pages\CreateContactInfo::route('/create'), 'edit' => Pages\EditContactInfo::route('/{record}/edit')]; }
}