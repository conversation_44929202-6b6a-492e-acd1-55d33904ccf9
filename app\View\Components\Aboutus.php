<?php

namespace App\View\Components;

use App\Models\AboutUsSection; // 1. Import model Anda
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Aboutus extends Component
{
    /**
     * Properti publik untuk menampung data.
     * Properti ini akan otomatis tersedia di dalam file blade.
     * @var AboutUsSection|null
     */
    public ?AboutUsSection $sectionData;

    /**
     * Create a new component instance.
     */
    public function __construct()
    {
        // 2. Ambil data record tunggal dari database
        $this->sectionData = AboutUsSection::first();
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        // 3. Arahkan ke file view blade Anda
        return view('components.aboutus');
    }
}