<?php

namespace Database\Seeders;

use App\Models\Location;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $country = Location::create([
            'name' => 'Indonesia',
            'type' => 'country',
            'latitude' => -0.789275,
            'longitude' => 113.921327,
            'parent_location_id' => null
        ]);

        $provinceJabar = Location::create([
            'name' => 'Jawa Barat',
            'type' => 'province',
            'latitude' => -6.917464,
            'longitude' => 107.619125,
            'parent_location_id' => $country->id
        ]);

        $provinceJatim = Location::create([
            'name' => 'Jawa Timur',
            'type' => 'province',
            'latitude' => -7.536064,
            'longitude' => 112.238403,
            'parent_location_id' => $country->id
        ]);

        Location::create([
            'name' => 'Bandung',
            'type' => 'city',
            'latitude' => -6.914744,
            'longitude' => 107.609810,
            'parent_location_id' => $provinceJabar->id
        ]);

        Location::create([
            'name' => 'Surabaya',
            'type' => 'city',
            'latitude' => -7.257472,
            'longitude' => 112.752090,
            'parent_location_id' => $provinceJatim->id
        ]);
    }
}
