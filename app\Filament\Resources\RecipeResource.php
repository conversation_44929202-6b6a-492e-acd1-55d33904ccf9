<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RecipeResource\Pages;
use App\Jobs\SendNotification;
use App\Models\Ingredient;
use App\Models\Recipe;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Filament\Resources\Concerns\Translatable;
use App\Jobs\SendSubscriptionEmail;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Log;

class RecipeResource extends Resource
{
    use Translatable;

    protected static ?string $model = Recipe::class;
    protected static ?string $navigationIcon = 'heroicon-o-book-open';
    protected static ?string $navigationGroup = 'Recipes Management';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Section::make('Main Details')->schema([
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->live(onBlur: true)
                    ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                        if ($operation !== 'create') {
                            return;
                        }
                        $titleToSlug = '';
                        if (is_array($state)) {
                            $locale = app()->getLocale();
                            $titleToSlug = $state[$locale] ?? (array_values($state)[0] ?? '');
                        } elseif (is_string($state)) {
                            $titleToSlug = $state;
                        }
                        $set('slug', Str::slug($titleToSlug));
                    }),

                Forms\Components\TextInput::make('slug')->required()->unique(ignoreRecord: true)->disabled()->dehydrated(),

                Forms\Components\Select::make('category_id')
                    ->relationship('category', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),

                Forms\Components\Select::make('author_id')->relationship('author', 'name')->searchable()->required()->default(auth()->id()),
                Forms\Components\FileUpload::make('main_image_url')->image()->directory('recipes')->label('Main Image')->disk('public'),
                
                Forms\Components\Textarea::make('short_description')
                    ->required()
                    ->columnSpanFull(),

                Forms\Components\RichEditor::make('full_description')
                    ->required()
                    ->columnSpanFull(),
            ])->columns(2),

            Forms\Components\Section::make('Attributes')
                ->schema([
                    Forms\Components\Group::make()
                        ->schema([
                            Forms\Components\TextInput::make('cooking_time_value')
                                ->label('Cooking Time')
                                ->numeric()
                                ->minValue(1)
                                ->required()
                                ->dehydrated(false),

                            Forms\Components\Select::make('cooking_time_unit')
                                ->label('Unit')
                                ->options([
                                    'minutes' => 'Minutes',
                                    'hours' => 'Hours',
                                ])
                                ->default('minutes')
                                ->required()
                                ->dehydrated(false),
                        ])->columns(2),

                    Forms\Components\TextInput::make('serving_size')->numeric(),

                    Forms\Components\Select::make('difficulty_level')
                        ->options(['easy' => 'Easy / Mudah', 'medium' => 'Medium / Sedang', 'hard' => 'Hard / Sulit'])
                        ->default('medium'),

                    Forms\Components\Hidden::make('cooking_time')
                        ->dehydrateStateUsing(function (Forms\Get $get): ?int {
                            $value = (int) $get('cooking_time_value');
                            $unit = $get('cooking_time_unit');
                            if (empty($value)) { return null; }
                            if ($unit === 'hours') { return $value * 60; }
                            return $value;
                        }),
                ])
                ->columns(3)
                ->afterStateHydrated(function (Forms\Set $set, ?Model $record): void {
                    if (!$record || is_null($record->cooking_time)) { return; }
                    $totalMinutes = (int) $record->cooking_time;
                    if ($totalMinutes >= 60 && $totalMinutes % 60 === 0) {
                        $set('cooking_time_value', $totalMinutes / 60);
                        $set('cooking_time_unit', 'hours');
                    } else {
                        $set('cooking_time_value', $totalMinutes);
                        $set('cooking_time_unit', 'minutes');
                    }
                }),
            
            Forms\Components\Section::make('Ingredients')->schema([
                Forms\Components\Repeater::make('ingredients')
                    ->schema([
                        Forms\Components\Select::make('ingredient_id')
                            ->label('Ingredient')
                            ->options(Ingredient::query()->pluck('name', 'id'))
                            ->searchable()
                            ->required()
                            ->distinct(),

                        Forms\Components\TextInput::make('quantity')->required(),

                        Forms\Components\TextInput::make('notes')
                            ->helperText('e.g., "Well-chopped", "cincang halus"')
                            ->nullable(),
                    ])
                    ->columns(3)
                    ->defaultItems(1)
                    ->addActionLabel('Add Ingredient')
                    ->cloneable(),
            ]),

            Forms\Components\Section::make('Publication')->schema([
                Forms\Components\Toggle::make('is_published')->label('Published')->default(true),
                Forms\Components\DateTimePicker::make('published_at')->label('Publish Date')->default(now()),
            ]),
        ]);
    }

    public static function afterCreate(CreateRecord $page, $record): void
    {
        SendNotification::dispatch($record);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('main_image_url')->label('Image'),
                Tables\Columns\TextColumn::make('title')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('cooking_time')
                    ->label('Cooking Time')
                    ->formatStateUsing(fn($state): string => $state ? "{$state} min" : '-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('category.name')->sortable(),
                Tables\Columns\IconColumn::make('is_published')->boolean()->label('Published'),
                Tables\Columns\TextColumn::make('published_at')->dateTime()->sortable(),
            ])
            ->actions([Tables\Actions\EditAction::make()])
            ->bulkActions([Tables\Actions\BulkActionGroup::make([Tables\Actions\DeleteBulkAction::make()])]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRecipes::route('/'),
            'create' => Pages\CreateRecipe::route('/create'),
            'edit' => Pages\EditRecipe::route('/{record}/edit'),
        ];
    }
}