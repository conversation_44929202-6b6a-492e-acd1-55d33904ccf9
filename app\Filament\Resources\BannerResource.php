<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BannerResource\Pages;
use App\Models\Banner;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class BannerResource extends Resource
{
    protected static ?string $model = Banner::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';
    protected static ?string $navigationGroup = 'Content Management';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Banner Content')
                    ->columns(2)
                    ->schema([
                        // Menggunakan 'image_path' agar konsisten dengan model dan view
                        Forms\Components\FileUpload::make('image_path') // <-- DIUBAH
                            ->label('Banner Image')
                            ->image()
                            ->directory('banners') // File akan disimpan di 'storage/app/public/banners'
                            ->required()
                            ->columnSpanFull(),

                        Forms\Components\TextInput::make('title')
                            ->maxLength(255),

                        Forms\Components\TextInput::make('subtitle')
                            ->maxLength(255),
                    ]),

                Forms\Components\Section::make('Settings')
                    ->columns(2)
                    ->schema([
                        Forms\Components\TextInput::make('link_url')
                            ->label('Link URL')
                            ->url()
                            ->maxLength(255)
                            ->placeholder('https://example.com/link-tujuan'),

                        Forms\Components\Select::make('location')
                            ->required()
                            ->options([
                                'homepage' => 'Homepage',
                                'recipes' => 'Recipes',
                                'articles' => 'Articles',
                                'products' => 'Products',
                                'general' => 'General',
                            ]),

                        Forms\Components\TextInput::make('order_index')
                            ->label('Display Order')
                            ->numeric()
                            ->default(0)
                            ->required(),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->required()
                            ->default(true),
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Menampilkan gambar dari kolom 'image_path'
                Tables\Columns\ImageColumn::make('image_path') // <-- DIUBAH
                    ->label('Image'),

                Tables\Columns\TextColumn::make('title')
                    ->searchable(),

                Tables\Columns\TextColumn::make('location')
                    ->badge()
                    ->searchable(),

                Tables\Columns\ToggleColumn::make('is_active')
                    ->label('Active'),

                Tables\Columns\TextColumn::make('order_index')
                    ->label('Order')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('location')
                    ->options([
                        'homepage' => 'Homepage',
                        'recipes' => 'Recipes',
                        'articles' => 'Articles',
                        'products' => 'Products',
                        'general' => 'General',
                    ]),
                Tables\Filters\TernaryFilter::make('is_active'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->reorderable('order_index');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBanners::route('/'),
            'create' => Pages\CreateBanner::route('/create'),
            'edit' => Pages\EditBanner::route('/{record}/edit'),
        ];
    }
}