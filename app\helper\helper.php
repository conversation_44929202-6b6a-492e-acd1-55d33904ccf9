<?php
// app/helpers.php
use Illuminate\Support\Facades\Cache;
use App\Models\SiteSetting;

if (!function_exists('setting')) {
/**
* Get setting value by key.
*
* @param string $key
* @param mixed $default
* @return mixed
*/
function setting($key, $default = null)
{
    // Ambil semua settings, cache selamanya agar efisien
    $settings = Cache::rememberForever('settings', function () {
        // Konversi collection ke format 'key' => 'value'
        return SiteSetting::all()->pluck('setting_value', 'setting_key');
    });

        return $settings->get($key, $default);
    }
}