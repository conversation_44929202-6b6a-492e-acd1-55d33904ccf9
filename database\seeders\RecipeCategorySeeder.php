<?php

namespace Database\Seeders;

use App\Models\RecipeCategory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class RecipeCategorySeeder extends Seeder
{
    public function run(): void
    {
        $categoryName = ['en' => 'Bread & Pastry', 'id' => 'Roti & Pastry'];
        
        RecipeCategory::updateOrCreate(
            ['name->id' => $categoryName['id']],
            [
                'name' => $categoryName,
                'slug' => Str::slug($categoryName['en']),
                'description' => [
                    'en' => 'A collection of bread and pastry recipes.',
                    'id' => 'Kumpulan resep roti dan pastry.'
                ]
            ]
        );
    }
}