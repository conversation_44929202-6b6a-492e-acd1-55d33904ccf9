<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class AllTranslationsRequired implements ValidationRule
{
    protected array $requiredLocales;

    public function __construct(array $locales = ['en', 'id'])
    {
        $this->requiredLocales = $locales;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!is_array($value)) {
            $fail('The :attribute is not in a valid format.');
            return;
        }

        foreach ($this->requiredLocales as $locale) {
            if (!isset($value[$locale]) || trim(strip_tags($value[$locale])) === '') {
                $fail("The " . str_replace('_', ' ', $attribute) . " for the '{$locale}' language is required.");
                return;
            }
        }
    }
}