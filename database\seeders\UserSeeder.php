<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // ===================================================================
        // LANGKAH 1: RESET CACHE PERMISSION (SANGAT PENTING!)
        // ===================================================================
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // ===================================================================
        // LANGKAH 2: BUAT SEMUA PERMISSION YANG DIBUTUHKAN APLIKASI
        // ===================================================================
        $permissions = [
            // User Management
            'view_user', 'view_any_user', 'create_user', 'update_user', 'delete_user', 'delete_any_user',
            // Role Management (via Shield Resource)
            'view_role', 'view_any_role', 'create_role', 'update_role', 'delete_role', 'delete_any_role',
            // Site Settings
            'view_setting', 'view_any_setting', 'create_setting', 'update_setting', 'delete_setting', 'delete_any_setting',
            // Recipe & Ingredient
            'view_recipe', 'view_any_recipe', 'create_recipe', 'update_recipe', 'delete_recipe', 'delete_any_recipe',
            'view_recipe::category', 'view_any_recipe::category', 'create_recipe::category', 'update_recipe::category', 'delete_recipe::category', 'delete_any_recipe::category',
            'view_ingredient', 'view_any_ingredient', 'create_ingredient', 'update_ingredient', 'delete_ingredient', 'delete_any_ingredient',
            'view_ingredient::category', 'view_any_ingredient::category', 'create_ingredient::category', 'update_ingredient::category', 'delete_ingredient::category', 'delete_any_ingredient::category',
        ];

        foreach ($permissions as $permission) {
            Permission::findOrCreate($permission, 'web');
        }

        // ===================================================================
        // LANGKAH 3: BUAT ROLES DAN BERIKAN PERMISSION
        // ===================================================================
        $superAdminRole = Role::findOrCreate('Super Admin', 'web');
        $adminRole = Role::findOrCreate('Admin', 'web');
        $userRole = Role::findOrCreate('Panel User', 'web');

        // Beri izin ke Admin (semua KECUALI yang berhubungan dengan 'user' dan 'role')
        $adminPermissions = Permission::whereNotIn('name', [
            'view_user', 'view_any_user', 'create_user', 'update_user', 'delete_user', 'delete_any_user',
            'view_role', 'view_any_role', 'create_role', 'update_role', 'delete_role', 'delete_any_role'
        ])->pluck('name');
        $adminRole->syncPermissions($adminPermissions);

        // Beri izin ke Panel User (HANYA yang berhubungan dengan 'recipe' dan 'ingredient')
        $userPermissions = Permission::where('name', 'like', '%\_recipe%')
            ->orWhere('name', 'like', '%\_ingredient%')
            ->pluck('name');
        $userRole->syncPermissions($userPermissions);
        
        // ===================================================================
        // === PERUBAHAN KUNCI ADA DI SINI ===
        // Berikan SEMUA permission yang ada di database ke role Super Admin.
        // Ini akan membuat UserPolicy mengembalikan `true` untuk semua pengecekan.
        // ===================================================================
        $superAdminRole->syncPermissions(Permission::all());

        // ===================================================================
        // LANGKAH 4: BUAT USERS DAN TETAPKAN ROLES
        // ===================================================================
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            ['first_name' => 'Super', 'last_name' => 'Admin', 'username' => 'superadmin', 'nik' => 'AG0001', 'is_active' => true, 'password' => Hash::make('password')]
        )->assignRole($superAdminRole);

        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            ['first_name' => 'Admin', 'last_name' => 'Biasa', 'username' => 'admin', 'nik' => 'AG0002', 'is_active' => true, 'password' => Hash::make('password')]
        )->assignRole($adminRole);

        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            ['first_name' => 'Budi', 'last_name' => 'Santoso', 'username' => 'budisantoso', 'nik' => 'AG0003', 'is_active' => true, 'password' => Hash::make('password')]
        )->assignRole($userRole);
    }
}