<?php

namespace App\Filament\Resources\RecipeResource\Pages;

use App\Filament\Resources\RecipeResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditRecipe extends EditRecord
{
    use EditRecord\Concerns\Translatable;

    protected static string $resource = RecipeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $ingredients = $this->getRecord()->ingredients->map(function ($ingredient) {
            return [
                'ingredient_id' => $ingredient->id,
                'quantity'      => $ingredient->pivot->quantity,
                'notes'         => json_decode($ingredient->pivot->notes),
            ];
        });

        $data['ingredients'] = $ingredients->toArray();

        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $ingredientsData = $data['ingredients'];
        unset($data['ingredients']);

        $record->update($data);

        $pivotData = [];
        foreach ($ingredientsData as $ingredient) {
            if (isset($ingredient['ingredient_id']) && isset($ingredient['quantity'])) {
                $pivotData[$ingredient['ingredient_id']] = [
                    'quantity' => $ingredient['quantity'],
                    'notes'    => json_encode($ingredient['notes'] ?? null),
                ];
            }
        }

        $record->ingredients()->sync($pivotData);

        return $record;
    }
}