<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Salesperson extends Model
{
    use HasFactory;

    protected $table = 'salespersons';

    /**
     * Mendefinisikan level peran untuk Salesperson.
     * Key adalah nilai yang disimpan di database.
     * Value adalah label yang akan ditampilkan.
     */
    public const ROLES = [
        1 => 'SESCO (Level 1)',
        2 => 'HOA (Level 2)',
        3 => 'HOR (Level 3)',
    ];

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone_number',
        'status',
        'role', // Kolom untuk menyimpan key dari ROLES (1, 2, atau 3)
        'photo',
    ];

    /**
     * Casting untuk memastikan kolom 'role' selalu bertipe integer.
     */
    protected $casts = [
        'role' => 'integer',
    ];

    /**
     * Relasi many-to-many dengan Location.
     * Seorang sales bisa ditugaskan di banyak lokasi.
     */
    public function locations(): BelongsToMany
    {
        return $this->belongsToMany(Location::class, 'salesperson_location');
    }
}