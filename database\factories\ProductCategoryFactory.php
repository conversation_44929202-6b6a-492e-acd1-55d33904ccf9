<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductCategory>
 */
class ProductCategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Membuat nama kategori dari 1 atau 2 kata acak
        // $name = ucwords(fake()->words(rand(1, 2), true));

        return [
        //     'name' => $name,
        //     'slug' => Str::slug($name),
        //     'description' => fake()->paragraph(),
        ];
    }
}
