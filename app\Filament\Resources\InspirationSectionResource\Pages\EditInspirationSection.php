<?php

namespace App\Filament\Resources\InspirationSectionResource\Pages;

use App\Filament\Resources\InspirationSectionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

// Tambahkan use statement ini
use Filament\Resources\Pages\EditRecord\Concerns\Translatable;

class EditInspirationSection extends EditRecord
{
    // Dan tambahkan trait ini
    use Translatable;

    protected static string $resource = InspirationSectionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // LocaleSwitcher sekarang akan berfungsi dengan benar
            Actions\LocaleSwitcher::make(),
        ];
    }
}