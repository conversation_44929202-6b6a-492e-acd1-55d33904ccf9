<?php

namespace App\Filament\Resources\InspirationSectionResource\Pages;

use App\Filament\Resources\InspirationSectionResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageInspirationSections extends ManageRecords
{
    protected static string $resource = InspirationSectionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
