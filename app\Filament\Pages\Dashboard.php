<?php

namespace App\Filament\Pages;

use App\Filament\Widgets\ArticleChart;
use App\Filament\Widgets\ArticleStats;
use App\Filament\Widgets\RecipeChart;
use Filament\Pages\Dashboard as BaseDashboard;
use App\Filament\Widgets\RecipeStats;

class Dashboard extends BaseDashboard
{
    /**
     * Metode ini menimpa daftar widget default.
     * Hanya widget yang terdaftar di dalam array ini yang akan ditampilkan.
     */
    public function getWidgets(): array
    {
        return [
            // Daftarkan HANYA widget yang ingin Anda lihat
            RecipeStats::class,
            ArticleStats::class,
            ArticleChart::class,
            RecipeChart::class,
        ];
    }
}