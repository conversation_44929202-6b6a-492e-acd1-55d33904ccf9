<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SiteSettingResource\Pages;
use App\Models\SiteSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;

class SiteSettingResource extends Resource
{
    protected static ?string $model = SiteSetting::class;
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';
    protected static ?string $navigationGroup = 'Settings';
    protected static bool $shouldRegisterNavigation = true;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('setting_key')
                ->required()
                ->maxLength(255)
                ->disabled(fn (string $context): bool => $context === 'edit')
                ->autofocus()
                ->live(),

            Forms\Components\FileUpload::make('setting_value')
                ->label('Setting File (Image/Logo)')
                ->image()
                ->disk('public')
                ->directory('site-settings')
                ->imageEditor()
                ->hidden(function (callable $get): bool {
                    return !str_contains($get('setting_key') ?? '', '_logo');
                }),

            Forms\Components\TextInput::make('setting_value')
                ->label('Setting Value')
                ->maxLength(255)
                ->nullable()
                ->hidden(function (callable $get): bool {
                    return str_contains($get('setting_key') ?? '', '_logo');
                }),

            Forms\Components\Textarea::make('description')
                ->nullable()
                ->rows(3),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('setting_key')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\ImageColumn::make('setting_value')
                    ->label('Value (Image)')
                    ->disk('public')
                    ->hidden(fn ($record): bool => !str_contains(optional($record)->setting_key ?? '', '_logo')),

                Tables\Columns\TextColumn::make('setting_value')
                    ->label('Value (Text)')
                    ->limit(50)
                    ->searchable()
                    ->hidden(fn ($record): bool => str_contains(optional($record)->setting_key ?? '', '_logo')),

                Tables\Columns\TextColumn::make('description')
                    ->words(10)
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSiteSettings::route('/'),
            'create' => Pages\CreateSiteSetting::route('/create'),
            'edit' => Pages\EditSiteSetting::route('/{record}/edit'),
        ];
    }
}