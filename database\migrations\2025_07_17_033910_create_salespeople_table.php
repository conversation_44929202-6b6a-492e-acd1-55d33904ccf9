<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('salespersons', function (Blueprint $table) {
            // 1. Tambahkan kolom baru terlebih dahulu
            $table->integer('role')->nullable()->after('status');
            $table->string('photo')->nullable()->after('role');

            // 2. Hapus kolom lama dengan pengecekan
            // Cek apakah kolom location_id ada sebelum menghapusnya
            if (Schema::hasColumn('salespersons', 'location_id')) {
                // Laravel 9+ bisa mendeteksi foreign key secara otomatis saat drop
                // Jika Anda menggunakan Laravel versi lama, Anda mungkin perlu nama constraint-nya
                $table->dropForeign(['location_id']);
                $table->dropColumn('location_id');
            }

            // Cek apakah kolom is_supervisor ada sebelum menghapusnya
            if (Schema::hasColumn('salespersons', 'is_supervisor')) { // <-- INI BAGIAN PENTINGNYA
                $table->dropColumn('is_supervisor');
            }
        });

        // 3. Buat tabel pivot baru
        Schema::create('salesperson_location', function (Blueprint $table) {
            $table->id();
            $table->foreignId('salesperson_id')->constrained('salespersons')->onDelete('cascade');
            $table->foreignId('location_id')->constrained()->onDelete('cascade');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        // Method down() digunakan untuk membatalkan migrasi (rollback)
        Schema::table('salespersons', function (Blueprint $table) {
            // Cek jika kolom ada sebelum menghapusnya (best practice)
            if (Schema::hasColumn('salespersons', 'role')) {
                $table->dropColumn('role');
            }
            if (Schema::hasColumn('salespersons', 'photo')) {
                $table->dropColumn('photo');
            }

            // Tambahkan kembali kolom lama jika belum ada
            if (!Schema::hasColumn('salespersons', 'location_id')) {
                $table->foreignId('location_id')->nullable()->constrained();
            }
            if (!Schema::hasColumn('salespersons', 'is_supervisor')) {
                $table->boolean('is_supervisor')->default(false);
            }
        });

        Schema::dropIfExists('salesperson_location');
    }
};