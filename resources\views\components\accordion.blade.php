{{--
=================================================================================================
| Komponen Daftar Kolapsibel (Collapsible List / Accordion)                                     |
|                                                                                               |
| Menampilkan daftar item yang dapat diperluas dan diciutkan.                                   |
| Hanya satu item yang dapat terbuka pada satu waktu.                                           |
|                                                                                               |
| Props:                                                                                        |
|   - $items: Array. Berisi data untuk setiap item.                                             |
|     Struktur per item: ['title', 'content']                                                   |
|                                                                                               |
| Teknologi:                                                                                    |
|   - Blade: Untuk struktur templating.                                                         |
|   - Tailwind CSS: Untuk styling. Warna disesuaikan dengan gambar.                             |
|   - Alpine.js: Untuk mengelola state buka/tutup (accordion).                                  |
=================================================================================================
--}}

@props([
    // Mendefinisikan properti 'items' yang diharapkan.
    // Defaultnya adalah array kosong untuk mencegah error.
    'items' => []
])

<!-- Awal: Kontainer Utama Komponen -->
{{--
    x-data="{ open: 1 }"
    - Inisialisasi state Alpine.js.
    - 'open' adalah variabel yang menyimpan indeks item yang sedang terbuka.
    - Kita set nilainya ke 1 agar item pertama terbuka secara default.
--}}
<div x-data="{ open: 1 }" class="w-full max-w-4xl mx-auto p-4 font-sans">
    <div class="space-y-4">

        @forelse ($items as $item)
            {{--
                Looping untuk setiap item dalam data.
                $loop->iteration memberikan indeks berbasis 1 (1, 2, 3, ...).
            --}}
            <!-- Awal: Item Accordion -->
            <div class="bg-[#E2EACD] rounded-2xl transition-all duration-300">
                {{--
                    Header Item (Selalu Terlihat)
                    @click="open = (open === {{ $loop->iteration }} ? null : {{ $loop->iteration }})"
                    - Logika toggle Alpine.js.
                    - Jika item yang diklik SUDAH terbuka, tutup (set 'open' ke null).
                    - Jika item yang diklik BELUM terbuka, buka (set 'open' ke indeks item ini).
                --}}
                <div
                    @click="open = (open === {{ $loop->iteration }} ? null : {{ $loop->iteration }})"
                    class="flex justify-between items-center p-6 cursor-pointer"
                >
                    {{-- Judul Item --}}
                    <h3 class="text-2xl md:text-3xl font-semibold text-gray-800">
                        {{ $item['title'] }}
                    </h3>

                    {{-- Ikon Panah (berubah tergantung state) --}}
                    <div class="relative w-12 h-12 flex items-center justify-center">
                        {{-- Ikon untuk state TERBUKA --}}
                        <div x-show="open === {{ $loop->iteration }}" x-transition.opacity class="absolute inset-0 bg-[#707C4F] rounded-full flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M5 10l7-7m0 0l7 7m-7-7v18" transform="rotate(45 12 12)" />
                            </svg>
                        </div>
                        {{-- Ikon untuk state TERTUTUP --}}
                        <div x-show="open !== {{ $loop->iteration }}" x-transition.opacity>
                             <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2.5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M5 10l7-7m0 0l7 7m-7-7v18" transform="rotate(135 12 12)" />
                            </svg>
                        </div>
                    </div>
                </div>

                {{--
                    Konten Item (Hanya terlihat jika item terbuka)
                    x-show="open === {{ $loop->iteration }}"
                    - Menampilkan elemen ini hanya jika variabel 'open' sama dengan indeks item saat ini.
                    x-collapse: Plugin Alpine.js untuk transisi tinggi yang mulus.
                    x-transition: Fallback transisi jika x-collapse tidak digunakan.
                --}}
                <div x-show="open === {{ $loop->iteration }}" x-collapse>
                    <div class="px-6 pb-6 flex items-start gap-4">
                        {{-- Garis Vertikal --}}
                        <div class="w-1 bg-gray-800 self-stretch"></div>
                        {{-- Teks Konten --}}
                        <p class="text-gray-700 text-base leading-relaxed">
                            {{ $item['content'] }}
                        </p>
                    </div>
                </div>
            </div>
            <!-- Akhir: Item Accordion -->

        @empty
            {{-- Pesan jika tidak ada data item yang diberikan --}}
            <div class="bg-gray-100 rounded-2xl p-6 text-center text-gray-500">
                Tidak ada item untuk ditampilkan.
            </div>
        @endforelse

    </div>
</div>
<!-- Akhir: Kontainer Utama Komponen -->