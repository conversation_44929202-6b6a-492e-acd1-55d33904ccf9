<?php

namespace Database\Seeders;

use App\Models\InspirationSection;
use App\Models\Testimonial;
use Illuminate\Database\Seeder;

class InspirationSectionSeeder extends Seeder
{
    public function run(): void
    {
        $testimonial = Testimonial::where('is_approved', true)->first();

        if (!$testimonial) {
            $this->command->warn('No approved testimonials found, skipping InspirationSectionSeeder.');
            return;
        }

        // Hapus data lama jika ada untuk memastikan hanya ada satu baris
        InspirationSection::query()->delete();

        InspirationSection::create([
            // PERUBAHAN: Gunakan json_encode untuk data multibahasa
            'title_line_1' => [
                'en' => 'BAKING INSPIRATION & TIPS',
                'id' => 'INSPIRASI & TIPS MEMBUAT KUE'
            ],
            'title_line_2' => [
                'en' => 'FOR SWEET SUCCESS',
                'id' => 'UNTUK KESUKSESAN MANIS'
            ],
            'main_image_title_1' => [
                'en' => 'Best Ingredient For',
                'id' => '<PERSON><PERSON>'
            ],
            'main_image_title_2' => [
                'en' => 'Our Bakers',
                'id' => 'Juru Masak Kami'
            ],

            // Kolom yang tidak berubah
            'main_image' => 'placeholder.jpg',
            'featured_testimonial_id' => $testimonial->id,
            'instagram_thumbnail' => 'placeholder.jpg',
            'instagram_url' => 'https://instagram.com',
            'product_image' => 'placeholder.jpg',
            'tiktok_thumbnail' => 'placeholder.jpg',
            'tiktok_url' => 'https://tiktok.com',
        ]);
    }
}
