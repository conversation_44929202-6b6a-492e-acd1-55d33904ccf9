<?php

namespace App\Filament\Resources;

use App\Filament\Resources\IngredientCategoryResource\Pages;
use App\Models\IngredientCategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Filament\Resources\Concerns\Translatable;

class IngredientCategoryResource extends Resource
{
    use Translatable;

    protected static ?string $model = IngredientCategory::class;
    protected static ?string $navigationIcon = 'heroicon-o-tag';
    protected static ?string $navigationGroup = 'Ingredients Management';


public static function form(Form $form): Form
{
    return $form->schema([
        Forms\Components\Select::make('type')
            ->options(['ingredient' => 'Ingredient', 'topping' => 'Topping', 'filling' => 'Filling'])
            ->required()->default('ingredient'),

        Forms\Components\TextInput::make('sub_type')
            ->required()
            ->live(onBlur: true)
            ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                if ($operation !== 'create') {
                    return;
                }

                $titleToSlug = '';
                if (is_array($state)) {
                    $locale = app()->getLocale();
                    $titleToSlug = $state[$locale] ?? (array_values($state)[0] ?? '');
                } elseif (is_string($state)) {
                    $titleToSlug = $state;
                }

                $set('slug', Str::slug($titleToSlug));
            }),
        
        Forms\Components\TextInput::make('slug')
            ->required()
            ->unique(IngredientCategory::class, 'slug', ignoreRecord: true)
            ->disabled()->dehydrated(),
    ]);
}

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('type')->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'ingredient' => 'success', 'topping' => 'info',
                    })->sortable(),
                Tables\Columns\TextColumn::make('sub_type')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('slug'),
            ])
            ->actions([Tables\Actions\EditAction::make()])
            ->bulkActions([Tables\Actions\BulkActionGroup::make([Tables\Actions\DeleteBulkAction::make()])]);
    }
    
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListIngredientCategories::route('/'),
            'create' => Pages\CreateIngredientCategory::route('/create'),
            'edit' => Pages\EditIngredientCategory::route('/{record}/edit'),
        ];
    }    
}