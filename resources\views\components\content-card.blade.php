{{-- resources/views/components/content-card.blade.php --}}

{{-- Komponen ini menerima properti dari Class PHP-nya --}}

<div class="bg-white rounded-xl shadow-md overflow-hidden transition-transform duration-300 hover:-translate-y-1 h-full flex flex-col">
    <a href="{{ $url }}" class="flex flex-col h-full">
        
        <!-- Bagian Gambar & Kategori -->
        <div class="relative">
            <img class="w-full h-48 object-cover" 
                 src="{{ $imageUrl }}" 
                 alt="Gambar untuk {{ $title }}">
            
            <span class="absolute top-4 left-4 bg-green-800/90 text-white text-xs font-semibold px-3 py-1 rounded-full backdrop-blur-sm">
                {{ $category }}
            </span>
        </div>

        <!-- Bagian Konten Teks -->
        <div class="p-6 flex flex-col flex-grow">
            {{-- Metadata Fleksibel (Waktu Masak / Waktu Ba<PERSON>) --}}
            <div class="flex items-center text-sm text-gray-500 mb-3">
                {{-- Ikon <PERSON>s --}}
                @if ($metadataIcon === 'clock')
                    <svg class="w-4 h-4 mr-1.5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" /></svg>
                @elseif ($metadataIcon === 'book')
                    <svg class="w-4 h-4 mr-1.5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6-2.292m0 0V3.75m0 12.552a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6.75a2.25 2.25 0 0 1 2.25-2.25h3.75a2.25 2.25 0 0 1 2.25 2.25v1.5m0 6.052a2.25 2.25 0 0 0-2.25 2.25H6a2.25 2.25 0 0 0-2.25-2.25V6.75a2.25 2.25 0 0 0 2.25-2.25h3.75a2.25 2.25 0 0 0 2.25 2.25v1.5m0 6.052 2.25-2.25m0 0 2.25 2.25m-2.25-2.25v-2.25m0 2.25h-2.25" /></svg>
                @endif
                <span>{{ $metadata }}</span>
            </div>

            <h3 class="text-lg font-bold text-gray-900 mb-2 leading-tight hover:text-green-700 transition-colors duration-200">
                {{ $title }}
            </h3>

            <p class="text-gray-600 text-sm mb-4 flex-grow">
                {{ $description }}
            </p>

            <p class="text-xs text-gray-400 mt-auto">
                {{ $date }}
            </p>
        </div>
    </a>
</div>