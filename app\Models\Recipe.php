<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\Translatable\HasTranslations;
use Illuminate\Support\Facades\Storage;

class Recipe extends Model
{
    use HasFactory, HasTranslations;

    protected $fillable = [
        'title', 'slug', 'short_description', 'full_description', 'main_image_url',
        'cooking_time', 'serving_size', 'difficulty_level', 'category_id',
        'author_id', 'is_published', 'published_at'
    ];
    
    public array $translatable = ['title', 'short_description', 'full_description'];

    protected $casts = [
        'published_at' => 'datetime',
        'is_published' => 'boolean',
        'cooking_time' => 'integer',
    ];

    protected static function booted(): void
    {
        static::deleted(function (Recipe $recipe) {
            if ($recipe->main_image_url) {
                Storage::disk('public')->delete($recipe->main_image_url);
            }
        });

        static::updating(function (Recipe $recipe) {
            if ($recipe->isDirty('main_image_url')) {
                $oldImage = $recipe->getOriginal('main_image_url');
                if ($oldImage) {
                    Storage::disk('public')->delete($oldImage);
                }
            }
        });
    }

    public function getHumanReadableCookingTimeAttribute(): ?string
    {
        $totalMinutes = $this->cooking_time;

        if (is_null($totalMinutes) || $totalMinutes <= 0) {
            return null;
        }

        $hours = floor($totalMinutes / 60);
        $minutes = $totalMinutes % 60;

        $parts = [];
        if ($hours > 0) {
            $parts[] = $hours . ' ' . trans_choice('messages.hours', $hours);
        }
        if ($minutes > 0 || ($hours === 0 && $minutes > 0)) {
            $parts[] = $minutes . ' ' . trans_choice('messages.minutes', $minutes);
        }

        return implode(' ', $parts);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(RecipeCategory::class, 'category_id');
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id');
    }
    
    public function ingredients(): BelongsToMany
    {
        return $this->belongsToMany(Ingredient::class, 'recipe_ingredients')
            ->withPivot('quantity', 'notes')
            ->withTimestamps();
    }
}