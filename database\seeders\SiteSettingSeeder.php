<?php

namespace Database\Seeders;

use App\Models\SiteSetting;
use Illuminate\Database\Seeder;

class SiteSettingSeeder extends Seeder
{
    public function run(): void
    {
        SiteSetting::updateOrCreate(['setting_key' => 'site_name'], ['setting_value' => 'Moremade CMS']);
        SiteSetting::updateOrCreate(['setting_key' => 'site_description'], ['setting_value' => 'Welcome!']);
        SiteSetting::updateOrCreate(['setting_key' => 'contact_email'], ['setting_value' => '<EMAIL>']);
        SiteSetting::updateOrCreate(['setting_key' => 'contact_phone'], ['setting_value' => '']);
    }
}