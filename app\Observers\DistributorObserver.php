<?php

namespace App\Observers;

use App\Models\Distributor;
use App\Models\Salesperson;

class DistributorObserver
{
    public function creating(Distributor $distributor): void
    {
        if (is_null($distributor->salesperson_id) && !is_null($distributor->location_id)) {
            
            $salesperson = Salesperson::where('location_id', $distributor->location_id)
                ->where('status', 'active')
                ->first();

            if ($salesperson) {
                $distributor->salesperson_id = $salesperson->id;
                return;
            }

            $supervisor = Salesperson::where('is_supervisor', true)
                ->where('status', 'active')
                ->first();
            
            if ($supervisor) {
                $distributor->salesperson_id = $supervisor->id;
            }
        }
    }
}