<?php

namespace Database\Seeders;

use App\Models\ProductCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Hapus data lama (opsional, baik untuk testing)
        // ProductCategory::truncate(); 

        // Membuat 3 kategori dengan nama spesifik
        ProductCategory::create(['name' => 'Fryall', 'slug' => 'fryall', 'description' => ['en' => 'Products for frying.', 'id' => 'Produk untuk gorengan.']]);
        ProductCategory::create(['name' => 'Optima', 'slug' => 'optima', 'description' => ['en' => 'Products for cakes and cooking.', 'id' => 'Produk untuk kue dan masakan.']]);
        ProductCategory::create(['name' => 'Beverage', 'slug' => 'beverage', 'description' => ['en' => 'Products for drinks.', 'id' => 'Produk untuk minuman.']]);

        // Membuat 5 kategori lainnya secara acak menggunakan factory
        // ProductCategory::factory(5)->create();
    }
}
