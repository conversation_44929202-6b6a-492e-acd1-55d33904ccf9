@extends('layouts.app')

@section('title', $recipe->title)

@section('content')
<!-- Full Width Hero Image -->
<div class="w-full mb-12">
    <div class="aspect-w-16 aspect-h-9 h-screen">
        @if($recipe->main_image_url)
        <img src="{{ asset('storage/' . $recipe->main_image_url) }}" class="object-cover w-full h-full" alt="{{ $recipe->title }}">
        @else
        <div class="bg-gray-200 w-full h-full"></div>
        @endif
    </div>
</div>

<!-- Title & Meta -->
<div class="w-full px-6 sm:px-10 lg:px-16 text-center">
    {{-- $recipe->title is translated automatically by the Spatie package and our SetLocale middleware --}}
    <h1 class="font-lora text-5xl md:text-6xl font-bold text-[#3A1718]">{{ __('messages.recipe.title_prefix') }} {{ $recipe->title }}</h1>

    <div class="flex justify-center items-center flex-wrap gap-4 my-6 text-2xl">
        <!-- == START: COOKING TIME CHANGE == -->
        <!-- Cooking Time -->
        <span class="bg-gray-100 px-6 py-3 rounded-full font-medium flex items-center gap-2">
            <strong>{{ __('messages.recipe.cooking_time') }}</strong>
            <span class="bg-black text-white font-bold px-4 py-1 rounded-md">
                @php
                $cookingTimeDisplay = 'N/A';
                if ($recipe->cooking_time) {
                $value = null;
                $unit = null;

                // Case 1: Value is in the format "180 minutes"
                if (str_contains($recipe->cooking_time, ' ')) {
                $parts = explode(' ', $recipe->cooking_time, 2);
                if (is_numeric($parts[0])) {
                $value = (int) $parts[0];
                $unit = strtolower(trim($parts[1]));
                }
                }
                // Case 2: Value is just a number like "180"
                elseif (is_numeric($recipe->cooking_time)) {
                $value = (int) $recipe->cooking_time;
                $unit = 'minutes'; // Sensible default
                }

                // If we successfully parsed a value and a valid unit, translate it
                if ($value !== null && in_array($unit, ['minutes', 'hours'])) {
                $translatedUnit = trans_choice("messages.recipe.{$unit}", $value, [], app()->getLocale());
                $cookingTimeDisplay = "{$value} {$translatedUnit}";
                } else {
                // Fallback for any other format (e.g., "N/A", "Varies")
                $cookingTimeDisplay = $recipe->cooking_time;
                }
                }
                @endphp
                {{ $cookingTimeDisplay }}
            </span>
        </span>
        <!-- == END: COOKING TIME CHANGE == -->

        <!-- Difficulty Level -->
        @php
        $level = strtolower($recipe->difficulty_level ?? 'medium');
        $bgColorClass = 'bg-highlight-yellow text-highlight-yellow-text'; // Default for medium
        if (in_array($level, ['hard', 'sulit'])) $bgColorClass = 'bg-red-500 text-white';
        if (in_array($level, ['easy', 'mudah'])) $bgColorClass = 'bg-green-500 text-white';
        @endphp
        <span class="bg-gray-100 px-6 py-3 rounded-full font-medium flex items-center gap-2">
            <strong>{{ __('messages.recipe.difficulty') }}</strong>
            <span class="px-4 py-1 rounded-md font-bold {{ $bgColorClass }}">
                {{ __('messages.recipe.difficulty_' . $level) }}
            </span>
        </span>

        <!-- Serving Size -->
        <span class="bg-gray-100 px-6 py-3 rounded-full font-medium">
            <strong>{{ __('messages.recipe.serving_size') }}</strong> {{ $recipe->serving_size ?? 'N/A' }}
        </span>
    </div>

    @if($recipe->short_description)
    {{-- This also translates automatically --}}
    <p class="max-w-4xl mx-auto text-gray-600 text-xl leading-relaxed mt-6">
        {{ $recipe->short_description }}
    </p>
    @endif
</div>

{{-- The rest of your template remains the same... --}}

<div class="w-full px-6 sm:px-10 lg:px-16 grid grid-cols-1 lg:grid-cols-12 gap-16 mt-20 text-gray-800">
    <!-- Left: Instructions & Toppings -->
    <div class="lg:col-span-7 space-y-16">
        <!-- Instructions -->
        <div>
            <h1 class="text-5xl font-extrabold text-[#707B3E] mb-10 text-center">{{ __('messages.recipe.instructions') }}</h1>
            <div class="text-xl leading-relaxed text-justify whitespace-pre-wrap space-y-4">
                {{-- This translates automatically. Using {!! !!} to render HTML from RichEditor. --}}
                {!! $recipe->full_description !!}
            </div>
        </div>

        <!-- Topping -->
        @if ($groupedToppings->isNotEmpty())
        <div>
            <h1 class="text-5xl font-extrabold text-[#707B3E] mb-8 text-center">{{ __('messages.recipe.topping') }}</h1>
            @foreach ($groupedToppings as $subType => $items)
            <div class="mb-10">
                {{-- Sub-type name is auto-translated --}}
                <h3 class="text-3xl font-bold text-[#707B3E] mb-4">{{ $subType }}</h3>

                {{-- Highlighted Toppings --}}
                <div class="space-y-2 text-xl leading-relaxed">
                    @foreach ($items as $item)
                    @php $ingredient = $item['ingredient']; @endphp
                    @if ($ingredient->highlight)
                    <div>
                        <span class="bg-[#707B3E] text-white font-bold px-3 py-1 rounded-md inline-block">
                            {{ $ingredient->name }} <strong>{{ $ingredient->pivot->quantity }} {{ $ingredient->unit }}</strong>
                        </span>
                    </div>
                    @endif
                    @endforeach
                </div>

                {{-- Non-Highlighted Toppings --}}
                <ul class="list-disc pl-6 space-y-2 text-xl leading-relaxed mt-4">
                    @foreach ($items as $item)
                    @php $ingredient = $item['ingredient']; @endphp
                    @if (!$ingredient->highlight)
                    <li>
                        <span>{{ $ingredient->name }}</span>
                        <strong class="ml-2">{{ $ingredient->pivot->quantity }} {{ $ingredient->unit }}</strong>
                    </li>
                    @endif
                    @endforeach
                </ul>
            </div>
            @endforeach
        </div>
        @endif
    </div>

    <!-- Right: Ingredients -->
    <div class="lg:col-span-5 space-y-16 bg-gray-100 p-8 rounded-lg">
        <div>
            <h1 class="mx-auto p-4 text-5xl font-extrabold mb-12 text-center bg-[#707B3E] text-white rounded-md w-fit">{{ __('messages.recipe.ingredients') }}</h1>
            @foreach ($groupedIngredients as $subType => $items)
            <div class="mb-10">
                <h3 class="text-3xl font-bold px-4 py-2 mb-4 text-left">{{ $subType }}</h3>

                {{-- Highlighted Ingredients --}}
                <div class="space-y-3 text-xl leading-relaxed px-4">
                    @foreach ($items as $item)
                    @php $ingredient = $item['ingredient']; @endphp
                    @if ($ingredient->highlight)
                    <div>
                        <span class="bg-[#707B3E] text-white font-bold px-3 py-1 rounded-md inline-block">
                            {{ $ingredient->name }} <strong>{{ $ingredient->pivot->quantity }} {{ $ingredient->unit }}</strong>
                        </span>
                    </div>
                    @endif
                    @endforeach
                </div>

                {{-- Non-Highlighted Ingredients --}}
                <ul class="list-disc pl-10 space-y-2 text-xl leading-relaxed mt-4">
                    @foreach ($items as $item)
                    @php $ingredient = $item['ingredient']; @endphp
                    @if (!$ingredient->highlight)
                    <li>
                        <span>{{ $ingredient->name }}</span>
                        <strong class="ml-2">{{ $ingredient->pivot->quantity }} {{ $ingredient->unit }}</strong>
                    </li>
                    @endif
                    @endforeach
                </ul>
            </div>
            @endforeach
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="w-full px-6 sm:px-10 lg:px-16 mt-16">
    <div class="bg-[#522F2B] rounded-lg p-6 flex justify-start items-center">
        <div class="flex gap-6 px-6 py-3 bg-white border border-white rounded-full">
            <button class="bg-white text-[#522F2B] font-semibold text-xl py-2 px-8 rounded-full hover:bg-gray-200 transition">
                {{ __('messages.recipe.copy_button') }}
            </button>
            <button class="bg-[#707B3E] text-white font-semibold text-xl py-2 px-8 rounded-full hover:bg-green-800 transition">
                {{ __('messages.recipe.download_button') }}
            </button>
        </div>
    </div>
</div>

<!-- Popular Recipes -->
<div class="w-full px-6 sm:px-10 lg:px-16 mt-24">
    <h3 class="text-center text-4xl font-bold text-primary-brown tracking-wider mb-10">{{ __('messages.recipe.popular_title') }}</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
        @for ($i = 0; $i < 3; $i++)
            <div class="bg-white rounded-lg shadow-lg overflow-hidden transform hover:-translate-y-1.5 transition-transform duration-300">
            <img src="https://i.imgur.com/2aYw1aF.png" class="w-full h-64 object-cover" alt="Popular Recipe">
            <div class="p-6 bg-light-beige">
                <p class="text-lg text-gray-500 mb-2">Frylic / 23 Aug 2025</p>
                <h5 class="font-semibold text-xl text-gray-800 leading-tight">Lorem ipsum dolor sit amet, consectetur adipiscing elit</h5>
            </div>
    </div>
    @endfor
</div>
</div>
@endsection