<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ArticleResource\Pages;
use App\Filament\Resources\ArticleResource\RelationManagers;
use App\Models\Article;
use App\Models\articles;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Infolists\Components\Grid as ComponentsGrid;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section as ComponentsSection;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Spatie\Forms\Components\SpatieTranslatableContainer;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ArticleResource extends Resource
{
    use Translatable;

    protected static ?string $model = Article::class;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationGroup = 'Article Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make()->columns(3)->schema([
                    Grid::make()->columnSpan(2)->schema([
                        Section::make('Konten Artikel')
                            ->schema([
                                TextInput::make('title')
                                    ->label('Judul')
                                    ->required()
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(fn(Set $set, ?string $state) => $set('slug', Str::slug($state))),

                                Textarea::make('short_description')
                                    ->label('Deskripsi Singkat')
                                    ->rows(3),

                                RichEditor::make('content')
                                    ->label('Konten Lengkap')
                                    ->required()
                                    ->fileAttachmentsDisk('public')
                                    ->fileAttachmentsDirectory('attachments'),
                            ]),
                    ]),

                    Grid::make()->columnSpan(1)->schema([
                        Section::make('Status & Gambar')
                            ->schema([
                                FileUpload::make('main_image_url')
                                    ->label('Gambar Utama')
                                    ->image()
                                    ->disk('public')
                                    ->directory('article-images'),
                                Toggle::make('is_published')
                                    ->label('Publikasikan Artikel')
                                    ->default(false)
                                    ->live(),
                                DatePicker::make('published_at')
                                    ->label('Tanggal Publikasi')
                                    ->visible(fn(Get $get) => $get('is_published')),
                            ]),

                        Section::make('Metadata')
                            ->schema([
                                TextInput::make('slug')
                                    ->label('Slug URL')
                                    ->required()
                                    ->unique(ignoreRecord: true),
                                Select::make('category_id')
                                    ->label('Kategori')
                                    ->relationship('category', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->required(),
                                Select::make('author_id')
                                    ->label('Penulis')
                                    ->relationship('author', 'username')
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->default(auth()->id()),
                            ]),
                    ]),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('row_number')
                    ->label('No.')
                    ->rowIndex()
                    ->sortable(),
                    
                TextColumn::make('title')
                    ->limit(20)
                    ->searchable()
                    ->alignment(Alignment::Left),

                TextColumn::make('short_description')
                    ->wrap()
                    ->limit(30)
                    ->alignment(Alignment::Left),

                TextColumn::make('content')
                    ->limit(40)
                    ->width('50%')
                    ->wrap()
                    ->tooltip(fn(Article $record): string => $record->content)
                    ->alignment(Alignment::Left),

                IconColumn::make('is_published')
                    ->boolean()
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->label('Published')
                    ->searchable()
                    ->alignment(Alignment::Center),

                TextColumn::make('author.username')
                    ->label('Author')
                    ->searchable()
                    ->alignment(Alignment::Center),

                TextColumn::make('category.name')
                    ->label('Category')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable()
                    ->alignment(Alignment::Center),

                TextColumn::make('main_image_url')
                    ->label('Main Image')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->alignment(Alignment::Center),
                    
                TextColumn::make('published_at')
                    ->dateTime('d M y')
                    ->dateTimeTooltip()
                    ->sortable()
                    ->label('Published At')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->alignment(Alignment::Center),
            ])
            ->filters([
                TernaryFilter::make('is_published')
                    ->label('Status Publikasi')
                    ->boolean()
                    ->trueLabel('Sudah Publikasi')
                    ->falseLabel('Belum Publikasi')
                    ->native(false),

                SelectFilter::make('category_id')
                    ->label('Kategori')
                    ->relationship('category', 'name')
                    ->searchable()
                    ->multiple()
                    ->preload(),

                SelectFilter::make('author_id')
                    ->label('Penulis')
                    ->relationship('author', 'username')
                    ->searchable()
                    ->multiple()
                    ->preload(),

                // Filter 4: Untuk published_at (Date Range)
                Filter::make('published_at')
                    ->form([
                        DatePicker::make('published_from')->label('Dipublikasikan dari'),
                        DatePicker::make('published_until')->label('Sampai tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['published_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('published_at', '>=', $date),
                            )
                            ->when(
                                $data['published_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('published_at', '<=', $date),
                            );
                    })
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->iconButton(),
                Tables\Actions\EditAction::make()->iconButton(),
                Tables\Actions\DeleteAction::make()->iconButton(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                ComponentsGrid::make(3)->schema([

                    ComponentsGrid::make()->columnSpan(2)->schema([
                        ComponentsSection::make('Konten Artikel')->schema([
                            TextEntry::make('title')
                                ->label('')
                                ->size(TextEntry\TextEntrySize::Large)
                                ->weight('bold')
                                ->fontFamily('serif'),

                            TextEntry::make('short_description')
                                ->label('')
                                ->color('gray'),

                            TextEntry::make('content')
                                ->label('')
                                ->html()
                                ->prose(),
                        ]),
                    ]),

                    ComponentsGrid::make()->columnSpan(1)->schema([
                        ComponentsSection::make('Status & Gambar')->schema([
                            ImageEntry::make('main_image_url')
                                ->label('')
                                ->width('100%')
                                ->height(200)
                                ->extraImgAttributes(['class' => 'object-cover rounded-md']),

                            TextEntry::make('is_published')
                                ->label('Status')
                                ->badge() // Gunakan method ini untuk membuatnya terlihat seperti badge
                                ->formatStateUsing(fn(bool $state): string => $state ? 'Published' : 'Draft')
                                ->color(fn(bool $state): string => match ($state) {
                                    true => 'success',
                                    false => 'danger',
                                }),

                            TextEntry::make('published_at')
                                ->label('Dipublikasikan pada')
                                ->icon('heroicon-o-calendar-days')
                                ->dateTime('d F Y')
                                ->visible(fn($record) => $record->is_published),
                        ]),

                        ComponentsSection::make('Metadata')->schema([
                            TextEntry::make('slug')
                                ->label('Slug')
                                ->icon('heroicon-o-link')
                                ->copyable() // Agar slug mudah disalin
                                ->copyableState(fn(string $state): string => url($state)),

                            TextEntry::make('category.name')
                                ->label('Kategori')
                                ->icon('heroicon-o-tag'),

                            TextEntry::make('author.username')
                                ->label('Penulis')
                                ->icon('heroicon-o-user'),
                        ]),
                    ]),
                ]),
            ]);
    }
    
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListArticles::route('/'),
            'create' => Pages\CreateArticle::route('/create'),
            'edit' => Pages\EditArticle::route('/{record}/edit'),
        ];
    }
}
