<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductResource\Pages;
use App\Filament\Resources\ProductResource\RelationManagers;
use App\Models\Product;
use Filament\Forms;
use Illuminate\Support\Str;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Split;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Infolists\Components\Grid as ComponentsGrid;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section as ComponentsSection;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProductResource extends Resource
{
    use Translatable;

    protected static ?string $model = Product::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Catalog Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make()
                    ->columns(6)
                    ->schema([
                        Section::make([
                            TextInput::make('name')->required()->live(onBlur: true)->afterStateUpdated(fn(Set $set, ?string $state) => $set('slug', Str::slug($state))),
                            TextInput::make('slug')
                                ->required()
                                ->readOnly()
                                ->maxLength(50)
                                ->unique(ignoreRecord: true),
                            TextInput::make('brand')->required(),
                            Select::make('category_id')
                                ->relationship('category', 'name')
                                ->required(),
                        ])->columnSpan(3),

                        Section::make([
                            TextInput::make('flavor')->required(),
                            TextInput::make('color')->required(),
                            TextInput::make('pack_size')->required(),
                            TextInput::make('product_code')->required(),
                        ])->columnSpan(3),

                        Section::make([
                            FileUpload::make('image_url')
                                ->required()
                                ->helperText('Upload a product image. Recommended size is 300x300 pixels.')
                                ->visibility('public')
                                ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
                                ->maxSize(1024),
                        ])->columnSpan(4),

                        Section::make([
                            Toggle::make('is_featured')
                                ->required()
                                ->label('Is this category featured?')
                                ->helperText('Featured categories will be highlighted.'),
                        ])->columnSpan(2)->extraAttributes(['class' => 'h-full flex justify-center items-center']),
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->searchable()->sortable()->alignment(Alignment::Center),
                TextColumn::make('category.name')->label('Category')->sortable()->alignment(Alignment::Center),
                TextColumn::make('brand')->sortable()->alignment(Alignment::Center),
                TextColumn::make('flavor')->sortable()->alignment(Alignment::Center),
                TextColumn::make('color')->sortable()->alignment(Alignment::Center)->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('pack_size')->sortable()->alignment(Alignment::Center),
                ImageColumn::make('image_url')->label('Image')->circular()->toggleable(isToggledHiddenByDefault: true)->alignment(Alignment::Center)->extraImgAttributes([
                'alt' => 'image',
                'loading' => 'lazy',
                ]),
                IconColumn::make('is_featured')
                    ->boolean()
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->alignment(Alignment::Center),
                // TextColumn::make('is_featured')
                //     ->badge()
                //     ->formatStateUsing(fn(bool $state): string => $state ? 'Featured' : 'Not Featured')
                //     ->color(fn(bool $state): string => match ($state) {
                //         true => 'success',
                //         false => 'danger',
                //     })
                //     ->alignment(Alignment::Center)
                //     ->searchable(query: function (Builder $query, string $search): Builder {
                //         $search = strtolower($search);
                //         if (str_starts_with('featured', $search)) {
                //             return $query->orWhere('is_featured', true);
                //         }
                //         if (str_starts_with('not featured', $search)) {
                //             return $query->orWhere('is_featured', false);
                //         }
                //         return $query;
                //     }),
                ])
            ->filters([
                TernaryFilter::make('is_featured'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->iconButton(),
                Tables\Actions\EditAction::make()->iconButton(),
                Tables\Actions\DeleteAction::make()->iconButton(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                ComponentsGrid::make(5)->schema([

                    ComponentsSection::make('Product Details')
                        ->schema([
                            ComponentsGrid::make(2)->schema([
                                TextEntry::make('name'),
                                TextEntry::make('slug'),
                                TextEntry::make('brand'),
                                TextEntry::make('category.name')->label('Category'),
                                TextEntry::make('flavor'),
                                TextEntry::make('color'),
                                TextEntry::make('pack_size')->label('Pack Size'),
                                TextEntry::make('product_code')->label('Product Code'),
                            ]),
                        ])
                        ->columnSpan(3),

                    ComponentsSection::make('Image & Status')
                        ->schema([
                            ImageEntry::make('image_url')
                                ->hiddenLabel() 
                                ->height(250)
                                ->circular(),

                            IconEntry::make('is_featured')
                                ->label('Featured Status')
                                ->boolean() 
                                ->alignCenter(),
                        ])
                        ->columnSpan(2),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            // 'view' => Pages\ViewProduct::route('/{record}'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }
}
