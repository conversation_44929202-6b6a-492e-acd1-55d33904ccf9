<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AboutUsSectionResource\Pages;
use App\Models\AboutUsSection;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Concerns\Translatable;

class AboutUsSectionResource extends Resource
{
    use Translatable;

    protected static ?string $model = AboutUsSection::class;
    protected static ?string $navigationIcon = 'heroicon-o-information-circle';
    protected static ?string $navigationLabel = 'About Us Section';
    protected static ?string $navigationGroup = 'Home Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Button & Headline')->collapsible()->schema([
                    Forms\Components\TextInput::make('top_button_text')->label('Top Button Text')->required(),
                    Forms\Components\Textarea::make('headline')->label('Headline')->required(),
                ]),
                Forms\Components\Section::make('Content')->collapsible()->schema([
                    Forms\Components\RichEditor::make('description')->label('Description')->required(),
                ]),
                Forms\Components\Section::make('Learn More Button')->collapsible()->schema([
                    Forms\Components\TextInput::make('learn_more_button_text')->label('Button Text')->required(),
                    Forms\Components\TextInput::make('learn_more_button_link')->label('Button Link (URL)')->placeholder('/contact')->default('#'),
                ]),
                Forms\Components\Section::make('Images')->collapsible()->schema([
                    Forms\Components\FileUpload::make('image_left_top')->label('Image Left Top')->image()->directory('about-us')->required(),
                    Forms\Components\FileUpload::make('image_left_bottom')->label('Image Left Bottom')->image()->directory('about-us')->required(),
                    Forms\Components\FileUpload::make('image_right_top')->label('Image Right Top')->image()->directory('about-us')->required(),
                    Forms\Components\FileUpload::make('image_right_bottom')->label('Image Right Bottom')->image()->directory('about-us')->required(),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table->columns([])->actions([Tables\Actions\EditAction::make()]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAboutUsSections::route('/'),
            'edit' => Pages\EditAboutUsSection::route('/{record}/edit'),
        ];
    }

    public static function getUrl(string $name = 'index', array $parameters = [], bool $isAbsolute = true, ?string $panel = null, ?Model $tenant = null): string
    {
        $record = static::getModel()::firstOrCreate([]);
        $parameters['record'] = $record->id;

        if ($name === 'index') {
            return static::getUrl('edit', $parameters, $isAbsolute, $panel, $tenant);
        }

        return parent::getUrl($name, $parameters, $isAbsolute, $panel, $tenant);
    }
}