<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Location extends Model {
    use HasFactory;
    protected $fillable = ['name', 'type', 'latitude', 'longitude', 'parent_location_id'];
    public function parent(): BelongsTo { return $this->belongsTo(Location::class, 'parent_location_id'); }
    public function children(): HasMany { return $this->hasMany(Location::class, 'parent_location_id'); }
}