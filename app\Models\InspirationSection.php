<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
// PERUBAHAN 1: Tambahkan import untuk HasTranslations
use Spatie\Translatable\HasTranslations;

class InspirationSection extends Model
{
    use HasFactory, HasTranslations;

    public array $translatable = [
        'title_line_1',
        'title_line_2',
        'main_image_title_1',
        'main_image_title_2',
    ];

    // Properti $casts yang konflik telah dihapus.

    protected $guarded = [];

    public function featuredTestimonial(): BelongsTo
    {
        return $this->belongsTo(Testimonial::class, 'featured_testimonial_id');
    }
}