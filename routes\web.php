<?php

use App\Http\Controllers\ArticlesController;
use App\Http\Controllers\ArticleDetailController;
use App\Http\Controllers\CostumerController;
use App\Http\Controllers\DetailCatalogController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\RecipeController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\ListCatalogController;
use App\Http\Controllers\SubscriberController;

Route::get('/', function () {
    return view('home.index');
});

Route::get('/home', function () {
    return view('home.index');
});

Route::get('/beranda', function () {
    return view('home.index');
});

Route::get('/distributor', function () {
    return view('distributor.index');
});


Route::get('/recipes', [RecipeController::class, 'index'])->name('recipes.index');
Route::get('/recipes/{slug}', [RecipeController::class, 'show'])->name('recipes.show');

Route::get('/language/{locale}', [LanguageController::class, 'switch'])->name('language.switch');
Route::get('/catalog', [ListCatalogController::class, 'show'])->name('list-catalog-show');
Route::get('/catalog/detailcatalog/{slug}', [DetailCatalogController::class, 'show'])->name('list-catalog-detail');
Route::get('/articles', [ArticlesController::class, 'index'])->name('article.index');
Route::get('/articles/{slug}', [ArticlesController::class, 'show'])->name('article-detail-show');
Route::get('/costumer', [CostumerController::class, 'show'])->name('costumer-show');
Route::post('/subscribe', [SubscriberController::class, 'store'])->name('subscribe');

route::get('/getcategoryarticle', [ArticlesController::class, 'getCategories'])->name('get.category.article');
