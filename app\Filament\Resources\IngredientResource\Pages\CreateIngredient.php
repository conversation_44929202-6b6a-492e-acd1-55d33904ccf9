<?php

namespace App\Filament\Resources\IngredientResource\Pages;

use App\Filament\Resources\IngredientResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateIngredient extends CreateRecord
{
    // V3 FIX: Use the Translatable trait for the Create page
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = IngredientResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // V3 FIX: Add the LocaleSwitcher Action
            Actions\LocaleSwitcher::make(),
        ];
    }
}