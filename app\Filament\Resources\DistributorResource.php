<?php
namespace App\Filament\Resources;
use App\Filament\Resources\DistributorResource\Pages;
use App\Models\Distributor;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class DistributorResource extends Resource
{
    protected static ?string $model = Distributor::class;
    protected static ?string $navigationIcon = 'heroicon-o-truck';
    protected static ?string $navigationGroup = 'Distributor Management';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('name')->required(),
            Forms\Components\TextInput::make('phone_number'),
            Forms\Components\TextInput::make('email')->email(),
            Forms\Components\Select::make('location_id')
                ->relationship('location', 'name')
                ->searchable()->preload()->required(),
            Forms\Components\Select::make('salesperson_id')
                ->relationship('salesperson', 'first_name')
                ->searchable()->preload()
                ->helperText('Biarkan kosong untuk penugasan otomatis berdasarkan lokasi.'),
            Forms\Components\Select::make('status')
                ->options(['active' => 'Active', 'inactive' => 'Inactive', 'onboarding' => 'Onboarding'])
                ->required()->default('onboarding'),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('name')->searchable(),
            Tables\Columns\TextColumn::make('location.name')->sortable(),
            Tables\Columns\TextColumn::make('salesperson.first_name')
                ->label('Salesperson')
                ->formatStateUsing(fn ($record) => $record->salesperson ? $record->salesperson->first_name . ' ' . $record->salesperson->last_name : 'N/A')
                ->searchable(),
            Tables\Columns\BadgeColumn::make('status')->colors(['success' => 'active', 'danger' => 'inactive', 'warning' => 'onboarding']),
        ])->actions([Tables\Actions\EditAction::make()]);
    }
    
    public static function getPages(): array { return ['index' => Pages\ListDistributors::route('/'), 'create' => Pages\CreateDistributor::route('/create'), 'edit' => Pages\EditDistributor::route('/{record}/edit')]; }
}