<?php

namespace App\Filament\Widgets;

use App\Models\ArticleCategory;
use Filament\Widgets\ChartWidget;

class Article<PERSON>hart extends ChartWidget
{
    protected static ?string $heading = 'article categorization';
    protected string | int | array $columnSpan = 'full';
    protected static bool $isLazy = true;
    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $categoriesData = ArticleCategory::withCount('articles')->get();
        $labels = $categoriesData->pluck('slug')->toArray();
        $data = $categoriesData->pluck('articles_count')->toArray();
        // dd($categoriesData,$labels,$data);

        $colorPalette = [
            '#FF6384',
            '#36A2EB',
            '#FFCE56',
            '#4BC0C0',
            '#9966FF',
            '#FF9F40',
            '#E7E9ED',
            '#8D6E63',
            '#26A69A',
            '#D4E157',
            '#FF7043',
            '#78909C'
        ];

        $backgroundColors = [];
        $borderColors = [];

        foreach ($data as $index => $_) {
            $color = $colorPalette[$index % count($colorPalette)];

            $backgroundColors[] = $color . '1A';
            $borderColors[] = $color;
        }

        return [
            'datasets' => [
                [
                    'label' => 'article category',
                    'data' => $data ,
                    'backgroundColor' => $backgroundColors,
                    'borderColor' => $borderColors,
                    'borderWidth' => 1,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}
