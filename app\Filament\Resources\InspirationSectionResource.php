<?php

namespace App\Filament\Resources;

use App\Filament\Resources\InspirationSectionResource\Pages;
use App\Models\InspirationSection;
use App\Models\Testimonial;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
// PERUBAHAN 1: Tambahkan import untuk Translatable
use Filament\Resources\Concerns\Translatable;

class InspirationSectionResource extends Resource
{
    // PERUBAHAN 2: Tambahkan trait Translatable
    use Translatable;

    protected static ?string $model = InspirationSection::class;
    protected static ?string $navigationIcon = 'heroicon-o-sparkles';
    protected static ?string $navigationLabel = 'Inspiration Section';
    protected static ?string $navigationGroup = 'Home Management';

    public static function form(Form $form): Form
    {
        // Tidak ada perubahan di sini, trait Translatable akan bekerja secara otomatis
        return $form
            ->schema([
                Forms\Components\Section::make('Main Title')->collapsible()->schema([
                    Forms\Components\TextInput::make('title_line_1')->required(),
                    Forms\Components\TextInput::make('title_line_2')->required(),
                ]),
                Forms\Components\Section::make('Main Image (Left Column)')->collapsible()->schema([
                    Forms\Components\FileUpload::make('main_image')->image()->directory('inspiration')->required(),
                    Forms\Components\TextInput::make('main_image_title_1')->required()->label('Image Title (Line 1)'),
                    Forms\Components\TextInput::make('main_image_title_2')->required()->label('Image Title (Line 2)'),
                ]),
                Forms\Components\Section::make('Featured Story (Middle Column)')->description('Pilih testimonial mana yang akan ditampilkan.')->collapsible()->schema([
                    Forms\Components\Select::make('featured_testimonial_id')
                        ->label('Featured Testimonial')
                        ->options(Testimonial::where('is_approved', true)->pluck('customer_name', 'id'))
                        ->searchable()->required(),
                    Forms\Components\FileUpload::make('product_image')->label('Product Image (Below Story)')->image()->directory('inspiration')->required(),
                ]),
                Forms\Components\Section::make('Social Posts (Right Column)')->collapsible()->schema([
                    Forms\Components\FileUpload::make('instagram_thumbnail')->label('Instagram Thumbnail')->image()->directory('inspiration')->required(),
                    Forms\Components\TextInput::make('instagram_url')->label('Instagram Post URL')->url()->required(),
                    Forms\Components\FileUpload::make('tiktok_thumbnail')->label('TikTok Thumbnail')->image()->directory('inspiration')->required(),
                    Forms\Components\TextInput::make('tiktok_url')->label('TikTok Post URL')->url()->required(),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table->columns([]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInspirationSections::route('/'),
            'edit' => Pages\EditInspirationSection::route('/{record}/edit'),
        ];
    }
}