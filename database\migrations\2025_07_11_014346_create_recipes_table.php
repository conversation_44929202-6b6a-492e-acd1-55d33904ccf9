<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('recipes', function (Blueprint $table) {
            $table->id();
            $table->json('title'); 
            $table->string('slug')->unique();
            $table->json('short_description')->nullable(); 
            $table->json('full_description');
            $table->string('main_image_url')->nullable();
            
            $table->integer('cooking_time')->unsigned()->nullable()->comment('Total cooking time in minutes');
            
            $table->string('serving_size', 50)->nullable();
            $table->enum('difficulty_level', ['easy', 'medium', 'hard'])->default('medium');
            $table->foreignId('category_id')->constrained('recipe_categories')->cascadeOnDelete();
            $table->foreignId('author_id')->constrained('users')->cascadeOnDelete();
            $table->boolean('is_published')->default(false);
            $table->timestamp('published_at')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('recipes');
    }
};