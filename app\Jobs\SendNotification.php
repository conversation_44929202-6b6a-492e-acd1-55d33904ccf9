<?php

namespace App\Jobs;

use App\Mail\NewRecipe;
use App\Models\Subscriber;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendNotification implements ShouldQueue
{
    use Queueable;

    protected $recipe;
    
    /**
     * Create a new job instance.
     */
    public function __construct($recipe)
    {
        $this->recipe = $recipe;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $subscribers = Subscriber::pluck('email')->all();
        foreach ($subscribers as $email) {
            Mail::to($email)->send(new NewRecipe($this->recipe));
        }
    }
}
