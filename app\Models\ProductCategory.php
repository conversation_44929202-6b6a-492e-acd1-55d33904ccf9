<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class ProductCategory extends Model
{
    /** @use HasFactory<\Database\Factories\ProductCategoryFactory> */
    use HasFactory, HasTranslations;

    public array $translatable = ['name', 'description'];
    
    protected $fillable = [
        'name',
        'slug',
        'description',
        'is_featured',
    ];

}
