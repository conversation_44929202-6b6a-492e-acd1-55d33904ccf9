<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_categories', function (Blueprint $table) {
            // id: INT, AUTO_INCREMENT, PK
            $table->id();

            // name: VARCHAR(50), UNIQUE, NOT NULL
            $table->json('name', 50);

            // slug: VARCHAR(50), UNIQUE, NOT NULL
            $table->string('slug', 50)->unique();

            // description: TEXT
            $table->json('description')->nullable();

            // created_at & updated_at: DATETIME
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_categories');
    }
};
