<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('products', function (Blueprint $table) {
            // id: INT, AUTO_INCREMENT, PK
            $table->id();

            // name: VARCHAR(100), NOT NULL
            $table->json('name');

            // slug: VARCHAR(255), UNIQUE, NOT NULL
            $table->string('slug', 255)->unique();

            // brand: VARCHAR(50), DEFAULT 'Moremade'
            $table->string('brand', 50)->default('Moremade');

            // description: TEXT
            $table->json('description')->nullable();

            // flavor: VARCHAR(50)
            $table->json('flavor')->nullable();

            $table->json('color')->nullable();

            $table->string('pack_size', 50)->nullable();

            $table->string('image_url', 255)->nullable();

            $table->string('product_code', 50)->unique();


            $table->foreignId('category_id')
                ->nullable()
                ->constrained('product_categories')
                ->onDelete('set null');

            $table->boolean('is_featured')->default(false);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('products');
    }
};
