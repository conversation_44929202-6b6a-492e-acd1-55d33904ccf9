<?php

namespace Database\Seeders;

use App\Models\Ingredient;
use App\Models\IngredientCategory;
use Illuminate\Database\Seeder;

class IngredientSeeder extends Seeder
{
    public function run(): void
    {
        $cat_tepung = IngredientCategory::where('sub_type->id', '<PERSON>han <PERSON> & Tepung')->firstOrFail()->id;
        $cat_telur = IngredientCategory::where('sub_type->id', 'Produk Susu & Telur')->firstOrFail()->id;
        $cat_lemak = IngredientCategory::where('sub_type->id', 'Lemak & Mentega')->firstOrFail()->id;
        $cat_bumbu = IngredientCategory::where('sub_type->id', 'Bumbu & Garam')->firstOrFail()->id;
        $cat_ragi = IngredientCategory::where('sub_type->id', 'Ragi & Pengembang')->firstOrFail()->id;
        $cat_buah = IngredientCategory::where('sub_type->id', 'Buah')->firstOrFail()->id;
        $cat_vla = IngredientCategory::where('sub_type->id', 'Vla')->firstOrFail()->id;

        $ingredients = [
            ['name' => ['en' => 'High-Protein Flour', 'id' => 'Tepung Terigu Protein Tinggi'], 'unit' => 'g', 'category_id' => $cat_tepung],
            ['name' => ['en' => 'All-Purpose Flour', 'id' => 'Tepung Terigu Protein Sedang'], 'unit' => 'g', 'category_id' => $cat_tepung],
            ['name' => ['en' => 'Granulated Sugar', 'id' => 'Gula Pasir'], 'unit' => 'g', 'category_id' => $cat_tepung],
            ['name' => ['en' => 'Milk Powder', 'id' => 'Susu Bubuk'], 'unit' => 'g', 'category_id' => $cat_tepung],
            ['name' => ['en' => 'Bread Improver', 'id' => 'Bread Improver'], 'unit' => 'g', 'category_id' => $cat_ragi],
            ['name' => ['en' => 'Egg', 'id' => 'Telur'], 'unit' => 'g', 'category_id' => $cat_telur],
            ['name' => ['en' => 'Egg Yolk', 'id' => 'Kuning Telur'], 'unit' => 'g', 'category_id' => $cat_telur],
            ['name' => ['en' => 'Cold Fresh Milk', 'id' => 'Susu Segar Dingin'], 'unit' => 'g', 'category_id' => $cat_telur],
            ['name' => ['en' => 'Ice Water', 'id' => 'Air Es'], 'unit' => 'g', 'category_id' => $cat_telur],
            ['name' => ['en' => 'Salt', 'id' => 'Garam'], 'unit' => 'g', 'category_id' => $cat_bumbu],
            ['name' => ['en' => 'GOLD BULLION Marville', 'id' => 'GOLD BULLION Marville'], 'unit' => 'g', 'highlight' => true, 'category_id' => $cat_lemak],
            ['name' => ['en' => 'Instant Yeast', 'id' => 'Ragi Instan'], 'unit' => 'g', 'category_id' => $cat_ragi],
            ['name' => ['en' => 'Coconut Water', 'id' => 'Air Kelapa'], 'unit' => 'g', 'category_id' => $cat_telur],
            ['name' => ['en' => 'Cinnamon Powder', 'id' => 'Kayu Manis Bubuk'], 'unit' => 'tsp', 'category_id' => $cat_bumbu],
            ['name' => ['en' => 'Canned Peach', 'id' => 'Peach Kalengan'], 'unit' => 'g', 'category_id' => $cat_buah],
            ['name' => ['en' => 'Canned Pineapple', 'id' => 'Nanas Kalengan'], 'unit' => 'g', 'category_id' => $cat_buah],
            ['name' => ['en' => 'Instant Custard Powder', 'id' => 'Vla Instan'], 'unit' => 'g', 'category_id' => $cat_vla],
            ['name' => ['en' => 'Fresh Milk', 'id' => 'Fresh Milk'], 'unit' => 'g', 'category_id' => $cat_vla],
            ['name' => ['en' => 'Fresh Cream', 'id' => 'Fresh Cream'], 'unit' => 'g', 'category_id' => $cat_vla],
            ['name' => ['en' => 'Rum', 'id' => 'Rhum'], 'unit' => 'tbsp', 'category_id' => $cat_vla],
        ];

        foreach ($ingredients as $ingredientData) {
            Ingredient::updateOrCreate(
                ['name->id' => $ingredientData['name']['id']],
                [
                    'name' => $ingredientData['name'],
                    'unit' => $ingredientData['unit'],
                    'highlight' => $ingredientData['highlight'] ?? false,
                    'ingredient_category_id' => $ingredientData['category_id'],
                ]
            );
        }
    }
}