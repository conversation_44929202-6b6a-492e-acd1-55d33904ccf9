<?php

namespace App\Filament\Resources\IngredientCategoryResource\Pages;

use App\Filament\Resources\IngredientCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateIngredientCategory extends CreateRecord
{
    // V3 FIX: Use the Translatable trait for this page type
    use CreateRecord\Concerns\Translatable;
    
    protected static string $resource = IngredientCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // V3 FIX: Add the LocaleSwitcher Action
            Actions\LocaleSwitcher::make(),
        ];
    }
}