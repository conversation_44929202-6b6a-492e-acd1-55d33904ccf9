<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MailingStatusResource\Pages;
use App\Models\MailingStatus;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;

class MailingStatusResource extends Resource
{
    protected static ?string $model = MailingStatus::class;

    protected static ?string $navigationIcon = 'heroicon-o-envelope-open';
    protected static ?string $navigationGroup = 'Settings';
    protected static ?string $pluralModelLabel = 'Mailing Statuses';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Ganti TextInput dengan Select
                Forms\Components\Select::make('category')
                    ->options([
                        'Recipes' => 'Recipes',
                        'Articles' => 'Articles',
                        'Distributor' => 'Distributor',
                        'Salesperson' => 'Salesperson',
                    ])
                    ->required()
                    // Memastikan kategori unik, mengabaikan record saat ini pada mode edit
                    ->unique(ignoreRecord: true)
                    // Menonaktifkan pencarian jika daftarnya pendek
                    ->searchable(false), 
                Forms\Components\Toggle::make('is_active')
                    ->required()
                    ->default(true)
                    ->label('Active'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('category')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\ToggleColumn::make('is_active')
                    ->label('Active'),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('is_active')
                    ->options([
                        '1' => 'Active',
                        '0' => 'Inactive',
                    ])
                    ->label('Status'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMailingStatuses::route('/'),
            'create' => Pages\CreateMailingStatus::route('/create'),
            'edit' => Pages\EditMailingStatus::route('/{record}/edit'),
        ];
    }
}