<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Distributor extends Model {
    use HasFactory;
    protected $fillable = ['name', 'phone_number', 'email', 'address', 'location_id', 'salesperson_id', 'status'];
    protected $casts = ['status' => 'string'];
    public function location(): BelongsTo { return $this->belongsTo(Location::class); }
    public function salesperson(): BelongsTo { return $this->belongsTo(Salesperson::class); }
}