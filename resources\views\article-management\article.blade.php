@extends('layouts.app')

@section('title', __('messages.title.article'))

@push('css')
{{-- CSS Anda untuk no-scrollbar tetap sama dan tidak perlu diubah --}}
<style>
    .no-scrollbar::-webkit-scrollbar { display: none; }
    .no-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }
</style>
@endpush

@section('content')
<x-banner-display location="articles" />

<!-- * content -->
<div class="w-full flex flex-col py-8">
    {{-- <PERSON><PERSON>m Pencarian --}}
    <div class="w-full mb-6">
        <div class="relative max-w-lg mx-auto">
            <input type="search" placeholder="Cari artikel..."
                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-full focus:ring-green-500 focus:border-green-500">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg class="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" /></svg>
            </div>
        </div>
    </div>
    
        {{-- Tombol kategori sudah berada di tengah --}}
        <div class="flex items-center justify-center m-4 gap-2">
            <x-filter-categories :categories="$articleCategories" />
        </div>
</div>

<!-- * Daftar Artikel -->
<div class="px-4 pb-12 mx-auto max-w-7xl">
    @if($articles->isNotEmpty())
        <div class="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            @foreach ($articles as $article)
                <x-content-card 
                    :image-url="$article->image_path ? asset('storage/' . $article->image_path) : 'https://via.placeholder.com/400x250.png/EBF4EC?text=No+Image'"
                    :category="$article->category->name ?? 'Uncategorized'"
                    :title="$article->title"
                    :description="Str::limit($article->excerpt ?? '', 100)"
                    :date="$article->published_at?->format('d F Y') ?? 'Tanggal tidak tersedia'"
                    :url="route('article-detail-show', $article->slug)"
                    :metadata="$article->reading_time . ' min read'"
                    metadata-icon="book"
                />
            @endforeach
        </div>
    @else
        <div>
            <p>article tidak ditemukan</p>
        </div>
    @endif

    {{-- Pagination --}}
    <div class="mt-12">
        {{ $articles->withQueryString()->links() }}
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const articleContent = document.getElementById('article-content');
        const searchInput = document.getElementById('search-input');
        const categoryFilters = document.getElementById('category-filters');
        let searchTimeout;

        // Fungsi utama untuk mengambil artikel dengan AJAX
        async function fetchArticles(url, pushState = true) {
            // Tampilkan indikator loading (opsional, tapi disarankan)
            articleContent.style.opacity = '0.5';

            try {
                const response = await fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                const data = await response.text(); // Ambil HTML sebagai teks

                // Ganti konten dengan hasil dari AJAX
                articleContent.innerHTML = data;

                // Update URL browser tanpa me-reload halaman
                if (pushState) {
                    history.pushState({ path: url }, '', url);
                }
            } catch (error) {
                console.error('Gagal mengambil artikel:', error);
                // Anda bisa menampilkan pesan error kepada pengguna di sini
                alert('Terjadi kesalahan saat memuat artikel.');
            } finally {
                // Hilangkan indikator loading
                articleContent.style.opacity = '1';
            }
        }

        // Fungsi untuk membangun URL dengan parameter yang ada
        function buildUrl(key, value) {
            const url = new URL(window.location.href);
            // Reset page ke 1 setiap kali ada filter baru
            url.searchParams.set('page', '1');

            if (value) {
                url.searchParams.set(key, value);
            } else {
                url.searchParams.delete(key);
            }
            return url.href;
        }

        // Event listener untuk input pencarian (dengan debounce)
        searchInput.addEventListener('keyup', function (e) {
            clearTimeout(searchTimeout);
            const query = e.target.value;

            searchTimeout = setTimeout(() => {
                const url = buildUrl('search', query);
                fetchArticles(url);
            }, 500); // Tunggu 500ms setelah user berhenti mengetik
        });

        // Event listener untuk filter kategori (menggunakan event delegation)
        categoryFilters.addEventListener('click', function (e) {
            // Pastikan yang diklik adalah link filter
            const filterLink = e.target.closest('a.category-filter');
            if (filterLink) {
                e.preventDefault(); // Mencegah link me-reload halaman
                fetchArticles(filterLink.href);
            }
        });

        // Event listener untuk pagination (menggunakan event delegation)
        articleContent.addEventListener('click', function (e) {
            // Pastikan yang diklik adalah link pagination
            const paginationLink = e.target.closest('.pagination a');
            if (paginationLink) {
                e.preventDefault(); // Mencegah link me-reload halaman
                fetchArticles(paginationLink.href);
            }
        });

        // Menangani tombol back/forward browser
        window.addEventListener('popstate', function (e) {
            if (e.state && e.state.path) {
                fetchArticles(e.state.path, false); // false agar tidak push state lagi
            } else {
                // Handle kasus jika state kosong (misal: halaman awal)
                fetchArticles(location.href, false);
            }
        });
    });
</script>
@endpush