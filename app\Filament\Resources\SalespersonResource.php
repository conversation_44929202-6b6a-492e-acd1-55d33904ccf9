<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SalespersonResource\Pages;
use App\Models\Salesperson; // Pastikan model di-import
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\FileUpload;
use Filament\Tables\Columns\ImageColumn;

class SalespersonResource extends Resource
{
    protected static ?string $model = Salesperson::class;
    protected static ?string $navigationIcon = 'heroicon-o-briefcase';
    protected static ?string $navigationGroup = 'Distributor Management';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Section::make()->schema([
                FileUpload::make('photo')
                    ->label('Sales Photo')
                    ->image()
                    ->directory('sales-photos')
                    ->avatar(),
                Forms\Components\TextInput::make('first_name')->required(),
                Forms\Components\TextInput::make('last_name'),
                Forms\Components\TextInput::make('email')->email()->required()->unique(ignoreRecord: true),
                Forms\Components\TextInput::make('phone_number'),
                Forms\Components\Select::make('locations')
                    ->relationship('locations', 'name')
                    ->multiple()
                    ->preload()
                    ->searchable()
                    ->label('Location'),
                Forms\Components\Select::make('role')
                    ->options(Salesperson::ROLES) // <-- Menggunakan constant dari model
                    ->required()
                    ->label('Roles'),
                Forms\Components\Select::make('status')->options(['active' => 'Active', 'inactive' => 'Inactive'])->required()->default('active'),
            ])->columns(2)
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table->columns([
            ImageColumn::make('photo')->label('Photo')->circular(),
            Tables\Columns\TextColumn::make('first_name')->searchable(),
            Tables\Columns\TextColumn::make('last_name')->searchable(),
            Tables\Columns\TextColumn::make('locations.name')->badge()->label('Lokasi'),
            Tables\Columns\TextColumn::make('role')
                ->badge()
                ->sortable()
                ->formatStateUsing(fn ($state): string => Salesperson::ROLES[$state] ?? 'Tidak Diketahui') // <-- Format tampilan agar menampilkan label, bukan angka
                ->color(fn (int $state): string => match ($state) { // <-- Memberi warna berbeda untuk setiap peran
                    1 => 'gray',
                    2 => 'warning',
                    3 => 'success',
                    default => 'danger',
                }),
            Tables\Columns\TextColumn::make('status')->badge()->colors(['success' => 'active', 'danger' => 'inactive']),
        ])->actions([Tables\Actions\EditAction::make()]);
    }

    public static function getPages(): array
    {
        return ['index' => Pages\ListSalespeople::route('/'), 'create' => Pages\CreateSalesperson::route('/create'), 'edit' => Pages\EditSalesperson::route('/{record}/edit')];
    }
}