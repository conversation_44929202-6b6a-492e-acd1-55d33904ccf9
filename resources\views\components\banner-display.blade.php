{{-- resources/views/components/banner-display.blade.php --}}

{{-- Gunakan isNotEmpty() karena $banners adalah sebuah collection --}}
@if($banners->isNotEmpty())
<div class="relative w-full" aria-label="Banner Carousel">

    {{-- Kontainer untuk item-item slider yang bisa di-scroll --}}
    {{-- ID 'banner-container' ini PENTING untuk JavaScript Anda --}}
    <div id="banner-container" class="flex overflow-x-auto snap-x snap-mandatory no-scrollbar">
        
        {{-- Lakukan looping untuk setiap banner di dalam collection --}}
        @foreach($banners as $banner)
            {{-- Setiap item adalah sebuah link yang memenuhi seluruh lebar kontainer --}}
            <a href="{{ $banner->link_url }}" class="block relative w-full h-[60vh] bg-black text-white overflow-hidden flex-shrink-0 snap-start">

                {{-- Gambar Banner --}}
                <img 
                    src="{{ asset('storage/' . $banner->image_path) }}" 
                    alt="{{ $banner->title }}" 
                    class="object-cover w-full h-full" 
                    onerror="this.style.display='none';"
                >

                {{-- Konten Teks di atas Gambar --}}
                <div class="absolute inset-0 flex flex-col items-center justify-center text-center p-8 bg-black/40">
                    <h1 class="text-4xl md:text-6xl font-bold font-serif drop-shadow-lg">
                        {{ $banner->title }}
                    </h1>

                    @if($banner->subtitle)
                    <p class="mt-4 text-lg md:text-xl max-w-2xl drop-shadow-md">
                        {{ $banner->subtitle }}
                    </p>
                    @endif
                </div>
            </a>
        @endforeach
    </div>

    {{-- Tombol Navigasi (Previous) --}}
    {{-- ID 'prev-btn-banner' ini PENTING untuk JavaScript Anda --}}
    <button id="prev-btn-banner" class="absolute top-1/2 left-4 -translate-y-1/2 bg-black/30 text-white p-2 rounded-full hover:bg-black/50 focus:outline-none transition-colors z-10">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>
    </button>

    {{-- Tombol Navigasi (Next) --}}
    {{-- ID 'next-btn-banner' ini PENTING untuk JavaScript Anda --}}
    <button id="next-btn-banner" class="absolute top-1/2 right-4 -translate-y-1/2 bg-black/30 text-white p-2 rounded-full hover:bg-black/50 focus:outline-none transition-colors z-10">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>
    </button>

</div>
@endif