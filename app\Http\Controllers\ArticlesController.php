<?php

namespace App\Http\Controllers;

use App\Models\Article;
use App\Models\ArticleCategory;
use App\Models\Product;
use Illuminate\Http\Request;

class ArticlesController extends Controller
{
    /**
     * Menampilkan daftar artikel yang sudah terbit, dengan opsi filter.
     */
    public function index(Request $request)
    {
        $articleCategories = ArticleCategory::all();

        $articlesQuery = Article::with('category')
            ->where('is_published', true)
            ->whereNotNull('published_at');

        if ($request->filled('category')) {
            $articlesQuery->whereHas('category', function ($query) use ($request) {
                $query->where('slug', $request->category);
            });
        }

        $articles = $articlesQuery->latest('published_at')->paginate(9);

        return view('article-management.index', compact('articles', 'articleCategories'));
    }

    /**
     * Menampilkan detail satu artikel.
     * Menggunakan Route Model Binding untuk efisiensi.
     */
    public function show($slug)
    {
        $article_category = Article::where('slug', $slug)->firstOrFail();
        return view('article-management.detail', compact('article_category'));
    }
}
