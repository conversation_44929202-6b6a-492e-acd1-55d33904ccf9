<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Banner extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'subtitle',
        'image_path',
        'link_url',
        'order_index',
        'is_active',
        'location',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'order_index' => 'integer',
    ];

    protected static function booted()
    {
        static::updating(function ($banner) {
            if ($banner->isDirty('image_path') && !is_null($banner->getOriginal('image_path'))) {
                Storage::disk('public')->delete($banner->getOriginal('image_path'));
            }
        });

        static::deleting(function ($banner) {
            if (!is_null($banner->image_path)) {
                Storage::disk('public')->delete($banner->image_path);
            }
        });
    }
}