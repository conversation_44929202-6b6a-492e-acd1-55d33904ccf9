<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;
use App\Models\Banner;
use Illuminate\Database\Eloquent\Collection;

class BannerDisplay extends Component
{
    public Collection $banners;

    public function __construct(string $location = 'homepage')
    {
        $this->banners = Banner::where('is_active', true)
                             ->where('location', $location)
                             ->orderBy('order_index', 'asc')
                             ->get();
    }

    public function render(): View|Closure|string
    {
        return view('components.banner-display');
    }
}