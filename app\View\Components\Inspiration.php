<?php
namespace App\View\Components;

use App\Models\InspirationSection;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Inspiration extends Component
{
    public ?InspirationSection $sectionData;

    public function __construct()
    {
        $this->sectionData = InspirationSection::with('featuredTestimonial')->first();
    }

    public function render(): View|Closure|string
    {

        return view('components.inspiration');
    }
}