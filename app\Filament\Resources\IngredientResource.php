<?php

namespace App\Filament\Resources;

use App\Filament\Resources\IngredientResource\Pages;
use App\Models\Ingredient;
use App\Models\IngredientCategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
// V3 FIX #1: Add the main Translatable Trait
use Filament\Resources\Concerns\Translatable;

class IngredientResource extends Resource
{
    // V3 FIX #2: Use the trait
    use Translatable;

    protected static ?string $model = Ingredient::class;
    protected static ?string $navigationIcon = 'heroicon-o-beaker';
    protected static ?string $navigationGroup = 'Ingredients Management';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Select::make('ingredient_category_id')
                ->relationship(name: 'category', titleAttribute: 'sub_type')
                ->options(function () {
                    return IngredientCategory::all()->groupBy('type')->map(function ($group) {
                        return $group->pluck('sub_type', 'id');
                    });
                })
                ->searchable()->required()->label('Category'),
            
            // V3 FIX #3: Use a standard TextInput. The plugin will convert it automatically.
            Forms\Components\TextInput::make('name')->required()->unique(ignoreRecord: true),

            Forms\Components\TextInput::make('unit')->helperText('e.g., g, ml, sdt, buah'),
            Forms\Components\Toggle::make('highlight')->label('Is a key ingredient?'),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('category.sub_type')->label('Category')->sortable()->badge(),
                Tables\Columns\TextColumn::make('unit'),
                Tables\Columns\IconColumn::make('highlight')->boolean(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->relationship('category', 'sub_type'),
            ])
            ->actions([Tables\Actions\EditAction::make()])
            ->bulkActions([Tables\Actions\BulkActionGroup::make([Tables\Actions\DeleteBulkAction::make()])]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListIngredients::route('/'),
            'create' => Pages\CreateIngredient::route('/create'),
            'edit' => Pages\EditIngredient::route('/{record}/edit'),
        ];
    }
}