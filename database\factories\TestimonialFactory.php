<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Testimonial>
 */
class TestimonialFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'customer_name' => $this->faker->name(),
            'customer_title' => $this->faker->jobTitle(),
            'profile_image_url' => $this->faker->imageUrl(100, 100, 'people'),
            'review_text' => [
                'en' => fake()->paragraph(3),
                'id' => fake('id_ID')->paragraph(3),
            ],
            'rating' => $this->faker->randomFloat(1, 4, 5),
            'location_id' => $this->faker->numberBetween(1, 5), 
            'is_approved' => $this->faker->boolean(80), 
            'created_at' => $this->faker->dateTimeBetween('-1 year', 'now'), 
        ];
    }
}
