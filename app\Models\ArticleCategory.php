<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class ArticleCategory extends Model
{
    use HasTranslations;

    protected $fillable = [
        'name',
        'slug',
        'description',
    ];

    public $translatable = [
        'name',
        'description'
    ];

    protected $casts = [
        'name' => 'array',
        'description' => 'array',
    ];

    public function articles()
    {
        return $this->hasMany(Article::class, 'category_id');
    }

}
