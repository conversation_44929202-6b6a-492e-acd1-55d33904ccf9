<?php

namespace App\Filament\Pages;

use Filament\Actions\Action;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class ManageSiteTranslations extends Page implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    protected static string $view = 'filament.pages.manage-site-translations';
    protected static ?string $navigationIcon = 'heroicon-o-language';
    protected static ?string $navigationLabel = 'Site Translations';
    protected static ?string $title = 'Site Translations';
    protected static ?string $navigationGroup = 'Settings';

    public function mount(): void
    {
        $this->data = $this->loadMessageTranslations();
    }

    public function form(Form $form): Form
    {
        $groups = $this->getTranslationGroups();

        $tabs = [];
        foreach ($groups as $group) {
            $tabs[] = Tabs\Tab::make(Str::ucfirst($group))
                ->schema($this->getSchemaForGroup($group));
        }

        return $form
            ->schema([
                Tabs::make('Translations')->tabs($tabs),
            ])
            ->statePath('data');
    }

    protected function getSchemaForGroup(string $group): array
    {
        $en_translations_safe = $this->loadTranslationsForLocale('en');
        $schema = [];

        $group_keys_safe = array_filter(
            array_keys($en_translations_safe),
            fn($key) => Str::before($key, '_') === $group
        );
        
        foreach ($group_keys_safe as $safe_key) {
            $label = Str::of($safe_key)->after($group . '_')->replace('_', ' / ')->upper();

            $schema[] = Fieldset::make($safe_key)
                ->label($label)
                ->schema([
                    Grid::make(2)->schema([
                        TextInput::make("id.{$safe_key}")->label('Bahasa'),
                        TextInput::make("en.{$safe_key}")->label('English'),
                    ]),
                ]);
        }
        return $schema;
    }

    public function save(): void
    {
        $formData = $this->form->getState();
        
        $this->saveTranslations('id', $formData['id'] ?? []);
        $this->saveTranslations('en', $formData['en'] ?? []);

        Notification::make()
            ->title('Translations saved successfully')
            ->success()
            ->send();
    }
    
    protected function getFormActions(): array
    {
        return [
            Action::make('save')
                ->label(__('filament-panels::resources/pages/edit-record.form.actions.save.label'))
                ->submit('save'),
        ];
    }

    protected function loadMessageTranslations(): array
    {
        return [
            'id' => $this->loadTranslationsForLocale('id'),
            'en' => $this->loadTranslationsForLocale('en'),
        ];
    }

    /**
     * Memuat file terjemahan dan mengubah semua '.' menjadi '_' pada kunci array
     * agar aman untuk wire:model.
     */
    protected function loadTranslationsForLocale(string $locale): array
    {
        $filePath = lang_path("{$locale}/messages.php");
        if (!File::exists($filePath)) return [];
        
        $translations = include($filePath);
        if (!is_array($translations)) return [];

        $flatTranslations = Arr::dot($translations);
        
        $safeKeyTranslations = [];
        foreach ($flatTranslations as $key => $value) {
            $safeKey = str_replace('.', '_', $key);
            $safeKeyTranslations[$safeKey] = $value;
        }

        return $safeKeyTranslations;
    }
    
    /**
     * --- PERBAIKAN LOGIKA MENYIMPAN FINAL ---
     * Menyimpan terjemahan dengan aman, membedakan antara kunci level atas
     * (seperti 'header.recipes') dan kunci yang benar-benar bersarang.
     */
    protected function saveTranslations(string $locale, array $new_safe_key_translations): void
    {
        $filePath = lang_path("{$locale}/messages.php");
        
        // Muat struktur array asli dari file
        $original_translations = File::exists($filePath) ? (include $filePath) : [];
        if (!is_array($original_translations)) {
            $original_translations = [];
        }
        
        // Iterasi melalui data dari form
        foreach ($new_safe_key_translations as $safeKey => $value) {
            if ($value !== null) {
                // Kembalikan kunci dari format aman ('_') ke format asli ('.')
                $originalKey = str_replace('_', '.', $safeKey);

                // --- LOGIKA KRUSIAL YANG BARU ---
                // PERIKSA: Apakah kunci seperti 'header.recipes' ada di level atas?
                if (array_key_exists($originalKey, $original_translations)) {
                    // JIKA YA: Perbarui nilainya secara langsung.
                    $original_translations[$originalKey] = $value;
                } else {
                    // JIKA TIDAK: Gunakan Arr::set untuk menangani kunci yang benar-benar bersarang.
                    Arr::set($original_translations, $originalKey, $value);
                }
            }
        }
        
        // Tulis kembali array yang sudah dimodifikasi ke file
        $content = "<?php\n\nreturn " . var_export($original_translations, true) . ";\n";
        
        if (File::put($filePath, $content) === false) {
            Notification::make()
                ->title('Failed to save file!')
                ->body("Could not write to {$filePath}. Check file permissions.")
                ->danger()
                ->send();
        }
    }

    /**
     * Mengambil grup dari kunci yang sudah aman (menggunakan '_').
     */
    protected function getTranslationGroups(): array
    {
        $keys = array_keys($this->loadTranslationsForLocale('en'));
        $groups = [];
        foreach ($keys as $key) {
            $groups[] = Str::before($key, '_');
        }
        return array_unique($groups);
    }
}