<?php

namespace App\Filament\Resources\InspirationSectionResource\Pages;

use App\Filament\Resources\InspirationSectionResource;
use App\Models\InspirationSection;
use Filament\Resources\Pages\ListRecords;

class ListInspirationSections extends ListRecords
{
    protected static string $resource = InspirationSectionResource::class;

    public function mount(): void
    {
        $record = InspirationSection::firstOrCreate([]);

        $url = InspirationSectionResource::getUrl('edit', ['record' => $record]);

        redirect($url);
    }
}