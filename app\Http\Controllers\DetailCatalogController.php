<?php

namespace App\Http\Controllers;

use App\Models\DetailCatalog;
use App\Models\Product;
use Illuminate\Http\Request;

class DetailCatalogController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show($slug)
    {
        $detailCatalog = Product::where('slug', $slug)->firstOrFail();
        return view('catalog-management.detailcatalog', compact('detailCatalog'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(DetailCatalog $detailCatalog)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, DetailCatalog $detailCatalog)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DetailCatalog $detailCatalog)
    {
        //
    }
}
