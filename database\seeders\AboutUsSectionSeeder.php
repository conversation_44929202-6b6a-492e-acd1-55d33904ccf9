<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AboutUsSectionSeeder extends Seeder
{
    public function run(): void
    {
        if (DB::table('about_us_sections')->count() == 0) {
            DB::table('about_us_sections')->insert([
                [
                    'id' => 1,
                    // PERUBAHAN: Gunakan json_encode untuk data multibahasa
                    'top_button_text' => json_encode(['en' => 'About Us', 'id' => 'Tentang Kami']),
                    'headline' => json_encode(['en' => 'We Bake With The Best Ingredients And A Lot Of Love', 'id' => 'Kami Membuat Kue dengan Bahan Terbaik dan Penuh Cinta']),
                    'description' => json_encode(['en' => 'Our passion for baking drives us to create delicious treats that bring joy to your table. Every recipe is crafted with care, using only the finest, freshest ingredients available.', 'id' => 'Semangat kami dalam membuat kue mendorong kami untuk menciptakan suguhan lezat yang membawa kegembiraan di meja Anda. Setiap resep dibuat dengan hati-hati, hanya menggunakan bahan-bahan terbaik dan segar yang tersedia.']),
                    'learn_more_button_text' => json_encode(['en' => 'Learn More', 'id' => 'Pelajari Lebih Lanjut']),
                    
                    // Kolom yang tidak berubah
                    'learn_more_button_link' => '/about',
                    'image_left_top' => 'placeholders/about-1.jpg',
                    'image_left_bottom' => 'placeholders/about-2.jpg',
                    'image_right_top' => 'placeholders/about-3.jpg',
                    'image_right_bottom' => 'placeholders/about-4.jpg',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            ]);
        }
    }
}