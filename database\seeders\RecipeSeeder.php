<?php

namespace Database\Seeders;

use App\Models\Ingredient;
use App\Models\Recipe;
use App\Models\RecipeCategory;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class RecipeSeeder extends Seeder
{
    public function run(): void
    {
        $category = RecipeCategory::where('name->id', 'Roti & Pastry')->firstOrFail();
        $author = User::where('email', '<EMAIL>')->firstOrFail();
        $title = ['en' => 'Fruit Custard Bluder Bread', 'id' => 'Roti Bluder Vla Buah'];

        $recipe = Recipe::updateOrCreate(
            ['slug' => Str::slug($title['en'])],
            [
                'title' => $title,
                'short_description' => [
                    'en' => 'A classic, super-soft bluder bread with a vanilla custard filling and fresh fruit topping.',
                    'id' => 'Roti bluder klasik yang super lembut dengan isian vla vanila dan topping buah-buahan segar.'
                ],
                'full_description' => [
                    'en' => "Step 1: Combine all of Ingredient A and Ingredient B, mix until half-kneaded.\nStep 2: Add Ingredient C, mix until smooth and elastic.\nStep 3: Ferment the dough for 90 minutes.\nStep 4: Mix in Ingredient D, mix well, then ferment again for 15 minutes.\nStep 5: Weigh the dough into 80g portions, round them, and place in molds. Ferment for 90 minutes.\nStep 6: Bake at 180°C for 15 minutes.\nStep 7: For the custard, combine all custard ingredients and stir until thickened. Pipe onto the bread.\nStep 8: Garnish with fruit toppings.",
                    'id' => "Langkah 1: Campurkan semua Bahan A dan Bahan B, aduk hingga setengah kalis.\nLangkah 2: Masukkan Bahan C, aduk hingga kalis elastis.\nLangkah 3: Fermentasikan adonan selama 90 menit.\nLangkah 4: Campurkan Bahan D, aduk rata, lalu fermentasikan lagi selama 15 menit.\nLangkah 5: Timbang adonan seberat 80g, bulatkan, dan letakkan di cetakan. Fermentasikan 90 menit.\nLangkah 6: Panggang pada suhu 180°C selama 15 menit.\nLangkah 7: Untuk vla, campur semua bahan vla dan aduk hingga mengental. Semprotkan ke atas roti.\nLangkah 8: Hias dengan topping buah."
                ],
                'cooking_time' => 180, 
                'serving_size' => '10 pieces',
                'difficulty_level' => 'hard',
                'category_id' => $category->id,
                'author_id' => $author->id,
                'is_published' => true,
                'published_at' => now(),
            ]
        );

        $ingredientsData = [
            ['name_id' => 'Tepung Terigu Protein Tinggi', 'quantity' => '700', 'notes' => ['en' => 'Ingredient A', 'id' => 'Bahan A']],
            ['name_id' => 'Tepung Terigu Protein Sedang', 'quantity' => '300', 'notes' => ['en' => 'Ingredient A', 'id' => 'Bahan A']],
            ['name_id' => 'Bread Improver', 'quantity' => '5', 'notes' => ['en' => 'Ingredient A', 'id' => 'Bahan A']],
            ['name_id' => 'Gula Pasir', 'quantity' => '200', 'notes' => ['en' => 'Ingredient A', 'id' => 'Bahan A']],
            ['name_id' => 'Susu Bubuk', 'quantity' => '30', 'notes' => ['en' => 'Ingredient A', 'id' => 'Bahan A']],
            ['name_id' => 'Telur', 'quantity' => '50', 'notes' => ['en' => 'Ingredient B', 'id' => 'Bahan B']],
            ['name_id' => 'Kuning Telur', 'quantity' => '150', 'notes' => ['en' => 'Ingredient B', 'id' => 'Bahan B']],
            ['name_id' => 'Susu Segar Dingin', 'quantity' => '150', 'notes' => ['en' => 'Ingredient B', 'id' => 'Bahan B']],
            ['name_id' => 'Air Es', 'quantity' => '50', 'notes' => ['en' => 'Ingredient B', 'id' => 'Bahan B']],
            ['name_id' => 'Garam', 'quantity' => '12', 'notes' => ['en' => 'Ingredient C', 'id' => 'Bahan C']],
            ['name_id' => 'GOLD BULLION Marville', 'quantity' => '200', 'notes' => ['en' => 'Ingredient C', 'id' => 'Bahan C']],
            ['name_id' => 'Ragi Instan', 'quantity' => '15', 'notes' => ['en' => 'Ingredient D', 'id' => 'Bahan D']],
            ['name_id' => 'Air Kelapa', 'quantity' => '100', 'notes' => ['en' => 'Ingredient D', 'id' => 'Bahan D']],
            ['name_id' => 'Peach Kalengan', 'quantity' => '150', 'notes' => null],
            ['name_id' => 'Nanas Kalengan', 'quantity' => '150', 'notes' => null],
            ['name_id' => 'Kayu Manis Bubuk', 'quantity' => '4', 'notes' => null],
            ['name_id' => 'Vla Instan', 'quantity' => '250', 'notes' => ['en' => 'Custard Ing.', 'id' => 'Bahan Vla']],
            ['name_id' => 'Fresh Milk', 'quantity' => '500', 'notes' => ['en' => 'Custard Ing.', 'id' => 'Bahan Vla']],
            ['name_id' => 'Fresh Cream', 'quantity' => '300', 'notes' => ['en' => 'Custard Ing.', 'id' => 'Bahan Vla']],
            ['name_id' => 'Rhum', 'quantity' => '1', 'notes' => ['en' => 'Custard Ing.', 'id' => 'Bahan Vla']],
        ];

        $pivots = [];
        foreach ($ingredientsData as $data) {
            $ingredient = Ingredient::where('name->id', $data['name_id'])->first();
            if ($ingredient) {
                $pivots[$ingredient->id] = [
                    'quantity' => $data['quantity'],
                    'notes' => is_array($data['notes']) ? json_encode($data['notes']) : $data['notes'],
                ];
            }
        }
        $recipe->ingredients()->sync($pivots);
    }
}