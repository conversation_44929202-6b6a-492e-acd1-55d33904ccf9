<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Translatable\HasTranslations;

class Product extends Model
{
    /** @use HasFactory<\Database\Factories\ProductFactory> */
    use HasFactory, HasTranslations;

    public $translatable = [
        'name',
        'description',
        'flavor',
        'color'
    ];

    protected $fillable = [
        'name',
        'slug',
        'brand',
        'description',
        'flavor',
        'color',
        'pack_size',
        'image_url',
        'product_code',
        'category_id',
        'is_featured',
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(ProductCategory::class, 'category_id', 'id');
    }
}
