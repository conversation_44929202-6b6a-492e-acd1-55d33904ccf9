{{-- <PERSON><PERSON><PERSON> @if untuk memastikan komponen hanya dirender jika ada datanya --}}
@if($sectionData)
<div class="bg-white">
    <div class="container mx-auto p-8 md:p-16">
        <div class="flex flex-wrap md:flex-nowrap items-center justify-center gap-8">

            <!-- <PERSON><PERSON><PERSON> G<PERSON>bar <PERSON> -->
            <div class="flex flex-col gap-8 w-full md:w-auto">
                {{-- Gunakan Storage::url() untuk mendapatkan URL publik dari gambar --}}
                <img src="{{ Storage::url($sectionData->image_left_top) }}" alt="Gambar tentang kami 1" class="rounded-2xl shadow-md object-cover h-64 w-full md:h-auto md:w-64">
                <img src="{{ Storage::url($sectionData->image_left_bottom) }}" alt="Gambar tentang kami 2" class="rounded-2xl shadow-md object-cover h-64 w-full md:h-auto md:w-64">
            </div>

            <!-- <PERSON><PERSON><PERSON>gah -->
            <div class="flex flex-col items-center text-center gap-6 max-w-lg mx-4 order-first md:order-none">
                <button class="border border-gray-400 rounded-full px-5 py-1 text-sm text-gray-700 hover:bg-gray-100 transition font-sans">
                    {{-- Tampilkan data dari database --}}
                    {{ $sectionData->top_button_text }}
                </button>

                <h1 class="text-4xl md:text-5xl text-gray-800 leading-tight font-serif">
                    {{-- Tampilkan data dari database --}}
                    {{ $sectionData->headline }}
                </h1>

                <div class="text-base text-gray-600 font-sans">
                    {{-- Gunakan {!! !!} karena RichEditor menghasilkan HTML --}}
                    {!! $sectionData->description !!}
                </div>
                
                {{-- Ubah button menjadi tag <a> agar bisa memiliki link --}}
                <a href="{{ $sectionData->learn_more_button_link }}" class="bg-[#5c2c31] text-white rounded-full px-6 py-3 flex items-center gap-4 shadow-lg hover:bg-opacity-90 transition font-sans no-underline">
                    {{-- Tampilkan data dari database --}}
                    <span>{{ $sectionData->learn_more_button_text }}</span>
                    <span class="bg-white rounded-full w-8 h-8 flex items-center justify-center">
                        <i class="fa-solid fa-arrow-right text-[#5c2c31]"></i>
                    </span>
                </a>
            </div>

            <!-- Kolom Gambar Kanan -->
            <div class="flex flex-col gap-8 w-full md:w-auto">
                <img src="{{ Storage::url($sectionData->image_right_top) }}" alt="Gambar tentang kami 3" class="rounded-2xl shadow-md object-cover h-64 w-full md:h-auto md:w-64">
                <img src="{{ Storage::url($sectionData->image_right_bottom) }}" alt="Gambar tentang kami 4" class="rounded-2xl shadow-md object-cover h-64 w-full md:h-auto md:w-64">
            </div>

        </div>
    </div>
</div>
@endif