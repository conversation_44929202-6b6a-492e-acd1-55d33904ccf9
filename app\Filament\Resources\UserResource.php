<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationGroup = 'User Management';
    protected static ?int $navigationSort = 1;

    /**
     * KUNCI UTAMA: Method ini sudah benar.
     * Ia memastikan hanya user dengan role 'Super Admin' yang bisa melihat menu User Management.
     */
    public static function canViewAny(): bool
    {
        return auth()->user()->hasRole('Super Admin');
    }

    /**
     * Method ini berfungsi sebagai lapisan keamanan tambahan.
     * Meskipun sudah dikontrol oleh canViewAny(), ini memastikan tidak ada URL
     * yang bisa diakses secara tidak sengaja oleh non-Super Admin.
     */
    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        if (!auth()->user()->hasRole('Super Admin')) {
            $query->whereDoesntHave('roles', function (Builder $query) {
                $query->whereIn('name', ['Super Admin', 'Admin']);
            });
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('User Details')
                    ->schema([
                        Forms\Components\TextInput::make('first_name')
                            ->required()->maxLength(255),
                        Forms\Components\TextInput::make('last_name')
                            ->required()->maxLength(255),
                        Forms\Components\TextInput::make('nik')
                            ->label('NIK')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->minLength(6)->maxLength(6)->regex('/^AG\d{4}$/'),
                        Forms\Components\TextInput::make('email')
                            ->email()->required()->unique(ignoreRecord: true)->maxLength(255),
                        Forms\Components\Toggle::make('is_active')
                            ->label('Is Active')->required(),
                    ])->columns(2),

                Forms\Components\Section::make('Password')
                    ->schema([
                        Forms\Components\TextInput::make('password')
                            ->password()
                            ->required(fn(string $context): bool => $context === 'create')
                            ->dehydrateStateUsing(fn($state) => Hash::make($state))
                            ->dehydrated(fn($state) => filled($state))
                            ->revealable(),
                    ]),

                Forms\Components\Section::make('Roles')
                    ->schema([
                        Forms\Components\Select::make('roles')
                            ->relationship('roles', 'name')
                            ->multiple()
                            ->preload()
                            ->searchable()
                            // Logika ini sudah benar, tetapi sekarang hanya akan dieksekusi oleh Super Admin.
                            ->options(function () {
                                if (auth()->user()->hasRole('Super Admin')) {
                                    return Role::all()->pluck('name', 'id');
                                }
                                // Baris ini secara praktis tidak akan pernah dijalankan
                                // oleh role lain karena canViewAny(), tapi aman untuk dibiarkan.
                                return Role::where('name', 'Panel User')->pluck('name', 'id');
                            }),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Full Name')
                    ->searchable(['first_name', 'last_name']),
                Tables\Columns\TextColumn::make('nik')
                    ->label('NIK')
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('roles.name')
                    ->badge(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => $record->id !== auth()->id()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}