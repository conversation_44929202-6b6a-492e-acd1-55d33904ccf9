<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Testimonial extends Model
{
    /** @use HasFactory<\Database\Factories\TestimonialFactory> */
    use HasFactory, HasTranslations;

    protected $fillable = [
        'customer_name',
        'customer_title',
        'profile_image_url',
        'review_text',
        'rating',
        'location_id',
        'is_approved'
    ];

    protected $casts = [
        'review_text' => 'array',
        'rating' => 'float',
        'is_approved' => 'boolean',
        'created_at' => 'datetime',
    ];

    public $translatable = ['review_text'];

    public function location()
    {
        return $this->belongsTo(Location::class, 'location_id');
    }
}
