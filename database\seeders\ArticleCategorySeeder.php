<?php

namespace Database\Seeders;

use App\Models\ArticleCategory;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ArticleCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Hapus data lama (opsional, baik untuk testing)
        // ArticleCategory::truncate(); 

        // Membuat 3 kategori dengan nama spesifik
        ArticleCategory::create([
            'name' => ['en' => 'Business', 'id' => 'Bisnis'],
            'slug' => 'business',
            'description' => ['en' => 'Articles about business, finance, and entrepreneurship.', 'id' => 'Artikel seputar bisnis, keuangan, dan kewira<PERSON>an.']
        ]);

        ArticleCategory::create([
            'name' => ['en' => 'Health', 'id' => 'Kesehatan'],
            'slug' => 'health',
            'description' => ['en' => 'Articles about health and wellness.', 'id' => 'Artikel tentang kesehatan dan kebugaran.']
        ]);

        ArticleCategory::create([
            'name' => ['en' => 'Lifestyle', 'id' => 'Gaya Hidup'],
            'slug' => 'lifestyle',
            'description' => ['en' => 'Articles about lifestyle and culture.', 'id' => 'Artikel tentang gaya hidup dan budaya.']
        ]);

        // Membuat 5 kategori lainnya secara acak menggunakan factory
        // \App\Models\ArticleCategory::factory(5)->create();
    }
}
