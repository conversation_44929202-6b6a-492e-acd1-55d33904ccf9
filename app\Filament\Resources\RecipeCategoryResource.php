<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RecipeCategoryResource\Pages;
use App\Models\RecipeCategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Filament\Resources\Concerns\Translatable;

class RecipeCategoryResource extends Resource
{
    use Translatable;

    protected static ?string $model = RecipeCategory::class;
    protected static ?string $navigationIcon = 'heroicon-o-folder';
    protected static ?string $navigationGroup = 'Recipes Management';


    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('name')
                ->required()
                ->live(onBlur: true)
                ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                    if ($operation !== 'create') {
                        return;
                    }

                    $titleToSlug = '';
                    if (is_array($state)) {
                        $locale = app()->getLocale();
                        $titleToSlug = $state[$locale] ?? (array_values($state)[0] ?? '');
                    } elseif (is_string($state)) {
                        $titleToSlug = $state;
                    }

                    $set('slug', Str::slug($titleToSlug));
                }),
            Forms\Components\TextInput::make('slug')->required()->unique(ignoreRecord: true)->disabled()->dehydrated(),
            Forms\Components\Textarea::make('description')->columnSpanFull(),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('slug'),
            ])
            ->actions([Tables\Actions\EditAction::make()])
            ->bulkActions([Tables\Actions\BulkActionGroup::make([Tables\Actions\DeleteBulkAction::make()])]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRecipeCategories::route('/'),
            'create' => Pages\CreateRecipeCategory::route('/create'),
            'edit' => Pages\EditRecipeCategory::route('/{record}/edit'),
        ];
    }
}
