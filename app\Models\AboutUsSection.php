<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
// PERUBAHAN 1: Tambahkan import untuk HasTranslations
use Spatie\Translatable\HasTranslations;

class AboutUsSection extends Model
{
    // PERUBAHAN 2: Tambahkan trait HasTranslations
    use HasFactory, HasTranslations;

    protected $table = 'about_us_sections';

    // PERUBAHAN 3: Definisikan kolom yang dapat diterjemahkan
    public array $translatable = [
        'top_button_text',
        'headline',
        'description',
        'learn_more_button_text',
    ];

    protected $fillable = [
        'top_button_text',
        'headline',
        'description',
        'learn_more_button_text',
        'learn_more_button_link',
        'image_left_top',
        'image_left_bottom',
        'image_right_top',
        'image_right_bottom',
    ];
}