<?php

namespace App\Providers;

use App\Models\Article;
use App\Models\Recipe;
use App\Observers\ArticleObserver;
use App\Observers\RecipeObserver;
use Illuminate\Support\ServiceProvider;

class EvenProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Recipe::observe(RecipeObserver::class);
        Article::observe(ArticleObserver::class);
    }
}
