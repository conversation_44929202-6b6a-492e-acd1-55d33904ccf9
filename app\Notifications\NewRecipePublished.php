<?php

namespace App\Notifications;

use App\Models\Recipe;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewRecipePublished extends Notification
{
    use Queueable;

    public function __construct(public Recipe $recipe)
    {
    }

    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
                    ->subject('New Recipe Published: ' . $this->recipe->title)
                    ->greeting('Hello!')
                    ->line('A new delicious recipe has just been published on our site.')
                    ->line('Recipe: ' . $this->recipe->title)
                    ->action('View Recipe', url('/recipes/' . $this->recipe->slug))
                    ->line('Thank you for being a loyal subscriber!');
    }
}