<?php

namespace Database\Factories;

use App\Models\ArticleCategory;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class ArticleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Membuat judul dalam bahasa Inggris terlebih dahulu untuk slug
        $title_en = fake()->sentence(6);

        return [
            // Kolom JSON untuk data terjemahan
            'title' => [
                'en' => $title_en,
                'id' => fake('id_ID')->sentence(6)
            ],
            'slug' => Str::slug($title_en),
            'short_description' => [
                'en' => fake()->paragraph(2),
                'id' => fake('id_ID')->paragraph(2)
            ],
            'content' => [
                'en' => fake()->paragraphs(5, true),
                'id' => fake('id_ID')->paragraphs(5, true)
            ],

            // Kolom biasa
            'main_image_url' => fake()->imageUrl(1280, 720, 'posts', true),
            'is_published' => fake()->boolean(70), // 70% kemungkinan true

            // Foreign Keys (pastikan User dan ArticleCategory sudah ada)
            'author_id' => User::inRandomOrder()->first()->id,
            'category_id' => ArticleCategory::inRandomOrder()->first()->id,

            // Kolom timestamp akan diisi oleh state di bawah
            'published_at' => null,
        ];
    }

    /**
     * Indicate that the article is published.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function published(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'is_published' => true,
                'published_at' => fake()->dateTimeThisYear(),
            ];
        });
    }
}
