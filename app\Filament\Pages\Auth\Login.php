<?php

namespace App\Filament\Pages\Auth;

use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Pages\Auth\Login as BaseLogin;
use Illuminate\Validation\ValidationException;

class Login extends BaseLogin
{
    public function getLayout(): string
    {
        return 'layouts.filament-login';
    }

    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->schema([
                        $this->getNikFormComponent(),
                        $this->getPasswordFormComponent(),
                        $this->getRememberFormComponent(),
                    ])
                    ->statePath('data')
            ),
        ];
    }

    protected function getNikFormComponent(): Component
    {
        return TextInput::make('nik')
            ->label('NIK')
            ->placeholder('Insert NIK')
            ->required()
            ->autofocus()
            ->minLength(6)
            ->maxLength(6)
            ->regex('/^AG\d{4}$/')
            ->extraAttributes([
                'class' => 'mb-6',
            ])
            ->markAsRequired(false);
    }

    protected function getPasswordFormComponent(): Component
    {
        return TextInput::make('password')
            ->label('Password')
            ->required()
            ->password()
            ->placeholder('Insert password')
            ->revealable()
            ->extraAttributes([
                'class' => 'mb-6',
            ])
            ->markAsRequired(false);
    }

    protected function getCredentialsFromFormData(array $data): array
    {
        return [
            'nik' => $data['nik'],
            'password' => $data['password'],
            'is_active' => true,
        ];
    }

    protected function throwFailureValidationException(): never
    {
        throw ValidationException::withMessages([
            'data.nik' => __('filament-panels::pages/auth/login.messages.failed'),
        ]);
    }
}
