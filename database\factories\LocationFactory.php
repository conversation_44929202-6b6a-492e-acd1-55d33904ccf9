<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Location>
 */
class LocationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = $this->faker->randomElement(['province', 'city', 'region', 'country']);

        $name = match ($type) {
            'country' => $this->faker->unique()->country(),
            'province' => $this->faker->unique()->state(),
            default => $this->faker->unique()->city(),
        };

        return [
            'name' => $name,
            'type' => $type,
            'latitude' => $this->faker->latitude(),
            'longitude' => $this->faker->longitude(),
            'parent_location_id' => null,
        ];
    }
}
