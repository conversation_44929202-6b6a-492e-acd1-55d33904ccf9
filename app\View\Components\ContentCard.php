<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class ContentCard extends Component
{
    /**
     * Buat instance komponen baru.
     * Properti publik ini akan menjadi variabel di dalam file Blade.
     */
    public function __construct(
        public string $imageUrl,
        public string $category,
        public string $title,
        public string $description,
        public string $date,
        public string $url,
        public string $metadata,
        public string $metadataIcon = 'clock' // Default icon adalah jam
    ) {
        // Konstruktor ini secara otomatis akan menetapkan semua parameter
        // ke properti publik dengan nama yang sama (PHP 8 feature).
    }

    /**
     * Dapatkan view yang merepresentasikan komponen.
     */
    public function render(): View|Closure|string
    {
        return view('components.content-card');
    }
}