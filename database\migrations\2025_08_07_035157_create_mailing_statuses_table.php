<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Membuat tabel 'mailing_statuses'
        Schema::create('mailing_statuses', function (Blueprint $table) {
            $table->id();
            // Kategori email, contoh: 'newsletter', 'promo', 'system_notification'
            $table->string('category')->unique();
            // Status untuk mengaktifkan/menonaktifkan pengiriman email kategori ini
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mailing_statuses');
    }
};