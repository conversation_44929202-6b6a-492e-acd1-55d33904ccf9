<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('inspiration_sections', function (Blueprint $table) {
            $table->id();

            // PERUBAHAN: Ubah kolom string menjadi text untuk menampung JSON
            $table->text('title_line_1');
            $table->text('title_line_2');
            $table->text('main_image_title_1');
            $table->text('main_image_title_2');

            // Kolom yang tidak berubah
            $table->string('main_image');
            $table->foreignId('featured_testimonial_id')->nullable()->constrained('testimonials')->nullOnDelete();
            $table->string('instagram_thumbnail');
            $table->string('instagram_url');
            $table->string('product_image');
            $table->string('tiktok_thumbnail');
            $table->string('tiktok_url');
            
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('inspiration_sections');
    }
};