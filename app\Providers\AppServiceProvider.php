<?php

namespace App\Providers;

use App\Models\Article;
use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use App\Models\Distributor;         // <-- IMPORT model Distributor
use App\Observers\ArticleObserver;
use App\Observers\DistributorObserver; // <-- IMPORT Observer

class AuthServiceProvider extends ServiceProvider
{
    protected $policies = [
    ];

    public function boot(): void
    {
        $this->registerPolicies();

        Gate::before(function ($user, $ability) {
            return $user->hasRole('Super Admin') ? true : null;
        });
        Distributor::observe(DistributorObserver::class);
    }
}
