<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class RecipeCategory extends Model
{
    use HasFactory, HasTranslations;

    protected $fillable = ['name', 'slug', 'description'];
    public array $translatable = ['name', 'description'];

    public function recipes(){
        return $this->hasMany(Recipe::class, 'category_id');
    }
}