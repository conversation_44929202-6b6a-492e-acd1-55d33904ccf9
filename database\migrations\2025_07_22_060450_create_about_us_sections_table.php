<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('about_us_sections', function (Blueprint $table) {
            $table->id();
            // PERUBAHAN: Ubah kolom string menjadi text untuk menampung JSON
            $table->text('top_button_text');
            $table->text('headline');
            $table->text('description');
            $table->text('learn_more_button_text');
            
            // Kolom yang tidak berubah
            $table->string('learn_more_button_link')->default('#');
            $table->string('image_left_top');
            $table->string('image_left_bottom');
            $table->string('image_right_top');
            $table->string('image_right_bottom');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('about_us_sections');
    }
};