<?php

namespace App\Filament\Resources\RecipeResource\Pages;

use App\Filament\Resources\RecipeResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateRecipe extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = RecipeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }

    protected function handleRecordCreation(array $data): Model
    {
        $ingredientsData = $data['ingredients'];
        unset($data['ingredients']);

        $recipe = static::getModel()::create($data);

        $pivotData = [];
        foreach ($ingredientsData as $ingredient) {
            if (isset($ingredient['ingredient_id']) && isset($ingredient['quantity'])) {
                $pivotData[$ingredient['ingredient_id']] = [
                    'quantity' => $ingredient['quantity'],
                    'notes'    => json_encode($ingredient['notes'] ?? null),
                ];
            }
        }

        if (!empty($pivotData)) {
            $recipe->ingredients()->attach($pivotData);
        }

        static::getResource()::afterCreate($this, $recipe);

        return $recipe;
    }
}