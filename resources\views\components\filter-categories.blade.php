<div class="my-7">
    <form method="GET" action="{{ route('article.index') }}">
        <div class="flex flex-wrap items-center gap-3">

            <button type="submit" name="category" value=""
                class="px-4 py-2 text-sm font-semibold rounded-full shadow-sm transition-colors duration-200 {{ !request('category') ? 'text-white bg-green-600 hover:bg-green-700' : 'text-gray-700 bg-gray-200 hover:bg-gray-300' }}">
                Semua
            </button>

            @foreach ($categories as $category)
            <button type="submit" name="category" value="{{ $category->slug }}"
                class="px-4 py-2 text-sm font-semibold rounded-full shadow-sm transition-colors duration-200 {{ request('category') == $category->slug ? 'text-white bg-green-600 hover:bg-green-700' : 'text-gray-700 bg-gray-200 hover:bg-gray-300' }}">
                {{ $category->name }}
            </button>
            @endforeach

        </div>
    </form>
</div>
