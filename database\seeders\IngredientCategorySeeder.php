<?php

namespace Database\Seeders;

use App\Models\IngredientCategory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class IngredientCategorySeeder extends Seeder
{
    public function run(): void
    {
        $categories = [
            ['type' => 'ingredient', 'sub_type' => ['en' => 'Dry Goods & Flour', 'id' => 'Bahan Kering & Tepung']],
            ['type' => 'ingredient', 'sub_type' => ['en' => 'Dairy & Eggs', 'id' => 'Produk Susu & Telur']],
            ['type' => 'ingredient', 'sub_type' => ['en' => 'Fats & Butter', 'id' => 'Lemak & Mentega']],
            ['type' => 'ingredient', 'sub_type' => ['en' => 'Spices & Salt', 'id' => 'Bumbu & Garam']],
            ['type' => 'ingredient', 'sub_type' => ['en' => 'Yeast & Leaveners', 'id' => 'Ragi & Pengembang']],
            ['type' => 'topping',    'sub_type' => ['en' => 'Fruit', 'id' => 'Buah']],
            ['type' => 'topping',    'sub_type' => ['en' => 'Custard / Vla', 'id' => 'Vla']],
        ];

        foreach ($categories as $categoryData) {
            IngredientCategory::updateOrCreate(
                [
                    'type' => $categoryData['type'],
                    'sub_type->id' => $categoryData['sub_type']['id'],
                ],
                [
                    'sub_type' => $categoryData['sub_type'],
                    'slug' => Str::slug($categoryData['sub_type']['en']),
                ]
            );
        }
    }
}