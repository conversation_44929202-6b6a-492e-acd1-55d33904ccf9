@extends('layouts.app')

@section('title',  __('messages.title.article_detail'))

@push('css')
<style>
    .no-scrollbar::-webkit-scrollbar {
        display: none;
    }

    .no-scrollbar {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }
</style>
@endpush

@section('content')
    <hr class="mt-20">
    <div class="flex items-center justify-center my-3 text-2xl">
        <h1 class="bg-gray-300 mx-1.5 py-0.5 pl-6 rounded-lg">Read Time: <span class="bg-black text-white py-0.5 px-4 rounded-lg">3m</span></h1>
        <h1 class="bg-amber-400 mx-1.5 to-whit py-0.5 px-6 rounded-lg">Event</h1>
    </div>
        <div class="mx-auto max-w-5xl my-10">
            <header class="mb-12 text-center">
                <h1 class="text-4xl md:text-5xl font-extrabold text-gray-900 leading-tight">
                    5 Simple Key To Make Your Bussiness Partner Feel Heard
                </h1>
            </header>
        </div>
    <div class="w-full h-[50vh] md:h-[65vh] bg-gray-200">
        <img class="w-full h-full object-cover" src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d"
            alt="Article main image">
    </div>
    
    <div class="container mx-auto max-w-7xl px-4 py-12">
        {{-- POIN 2: Layout Grid 3 Kolom (Share, Content, Author) --}}
        {{-- Layout ini aktif di layar besar (lg), dan akan bertumpuk di mobile --}}
        <div class="grid grid-cols-1 lg:grid-cols-[20%_auto_20%] lg:gap-12">
    
            <aside class="hidden lg:block">
                {{-- Dibuat sticky agar mengikuti saat scroll --}}
                <div class="sticky top-12 text-center">
                    <strong class="text-gray-800 font-semibold mb-4 block">Share:</strong>
                    <div class="flex flex-col items-center justify-center gap-5">
                        <a href="#" class="text-gray-500 hover:text-blue-600"> <svg class="h-6 w-6" fill="currentColor"
                                viewBox="0 0 24 24">
                                <path
                                    d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z" />
                            </svg></a>
                        <a href="#" class="text-gray-500 hover:text-sky-500"> <svg class="h-6 w-6" fill="currentColor"
                                viewBox="0 0 24 24">
                                <path
                                    d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616v.064c0 2.295 1.616 4.22 3.766 4.66-1.077.29-2.235.25-3.25.045 1.052 2.24 4.108 3.47 7.508 3.47-2.458 1.91-5.572 2.9-8.558 2.9-.556 0-1.11-.034-1.65-.095 2.96 1.908 6.472 2.9 10.208 2.9 12.234 0 18.92-10.12 18.5-19.04.88-.636 1.64-1.428 2.24-2.324z" />
                            </svg></a>
                        <a href="#" class="text-gray-500 hover:text-blue-700"> <svg class="h-6 w-6" fill="currentColor"
                                viewBox="0 0 24 24">
                                <path
                                    d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-4.481 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.59-11.018-3.714v-2.155z" />
                            </svg></a>
                    </div>
                </div>
            </aside>
    
            <main>
                <article class="prose prose-lg max-w-none prose-green">
                    <p>
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore
                        et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut
                        aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse
                        cillum dolore eu fugiat nulla pariatur.
                    </p>
                    <p>
                        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id
                        est laborum. Curabitur pretium tincidunt lacus. Nulla gravida orci a odio. Nullam varius, turpis et
                        commodo pharetra, est eros bibendum elit, nec luctus magna felis sollicitudin mauris.
                    </p>
                </article>
            </main>
    
            <aside class="hidden lg:block">
                {{-- Dibuat sticky agar mengikuti saat scroll --}}
                <div class="sticky top-12">
                    <div class="rounded-lg bg-gray-100 p-4 text-center">
                        <img class="h-16 w-16 mx-auto rounded-full object-cover"
                            src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde" alt="Author avatar">
                        <h4 class="mt-4 text-lg font-bold text-gray-900">Jhone Doe</h4>
                        <p class="mt-1 text-sm text-gray-700">
                            Business Consultant
                        </p>
                    </div>
                </div>
            </aside>
        </div>
    
        <div class="mx-auto max-w-2xl mt-16">
            <hr class="my-10">
    
            <div class="flex items-center gap-3">
                <strong class="text-gray-800">Tags:</strong>
                <div class="flex flex-wrap gap-2">
                    <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-700">#Business</span>
                    <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-700">#Partnership</span>
                </div>
            </div>
        </div>
            <hr class="my-16">
        <div>
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">You Might Also Like</h2>
            <div class="grid grid-cols-1 gap-8 md:grid-cols-4">
                {{-- Anda dapat me-looping artikel terkait di sini --}}
                @for ($i = 0; $i < 4; $i++)
                <div class="group">
                    <div class="overflow-hidden rounded-lg">
                        <img class="w-full h-56 object-cover transform transition-transform duration-300 group-hover:scale-105"
                            src="https://images.unsplash.com/photo-1552664730-d307ca884978" alt="Related article">
                    </div>
                    <div class="mt-4">
                        <span class="text-sm font-semibold text-green-600">EVENTS</span>
                        <h3 class="mt-2 text-xl font-bold text-gray-900 group-hover:text-green-700">
                            Cara Membangun Tim yang Solid dan Inovatif
                        </h3>
                        <p class="mt-2 text-sm text-gray-600">by Jane Smith - 12 June 2025</p>
                    </div>
                </div>
                @endfor
            </div>
        </div>
</div>
@endsection

@push('scripts')

@endpush