<?php

namespace App\Filament\Resources\MailingStatusResource\Pages;

use App\Filament\Resources\MailingStatusResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMailingStatuses extends ListRecords
{
    protected static string $resource = MailingStatusResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
