<?php

namespace App\Filament\Resources\IngredientResource\Pages;

use App\Filament\Resources\IngredientResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListIngredients extends ListRecords
{
    // V3 FIX: Use the Translatable trait for the List page
    use ListRecords\Concerns\Translatable;

    protected static string $resource = IngredientResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // V3 FIX: Add the LocaleSwitcher Action
            Actions\LocaleSwitcher::make(),
            Actions\CreateAction::make(),
        ];
    }
}