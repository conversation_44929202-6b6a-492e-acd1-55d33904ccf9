<?php

namespace App\Filament\Resources\RecipeCategoryResource\Pages;

use App\Filament\Resources\RecipeCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditRecipeCategory extends EditRecord
{
    // V3 FIX: Use the Translatable trait for the Edit page
    use EditRecord\Concerns\Translatable;

    protected static string $resource = RecipeCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // V3 FIX: Add the LocaleSwitcher Action
            Actions\LocaleSwitcher::make(),
            Actions\DeleteAction::make(),
        ];
    }
}