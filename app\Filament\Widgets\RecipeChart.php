<?php

namespace App\Filament\Widgets;

use App\Models\RecipeCategory;
use Filament\Widgets\ChartWidget;

class Recipe<PERSON>hart extends ChartWidget
{
    protected static ?string $heading = 'recipe categorization';
    protected string | int | array $columnSpan = 'full';
    protected static bool $isLazy = true;
    protected static ?int $sort = 4;

    protected function getData(): array
    {
        $categoriesData = RecipeCategory::withCount('recipes')->get();
        $labels = $categoriesData->pluck('slug')->toArray();
        $data = $categoriesData->pluck('recipes_count')->toArray();
        // dd($categoriesData,$labels,$data);

        $colorPalette = [
            '#D4E157',
            '#8D6E63',
            '#26A69A',
            '#E7E9ED',
            '#9966FF',
            '#FFCE56',
            '#FF6384',
            '#36A2EB',
            '#FF9F40',
            '#4BC0C0',
            '#78909C',
            '#FF7043',
        ];

        $backgroundColors = [];
        $borderColors = [];

        foreach ($data as $index => $_) {
            $color = $colorPalette[$index % count($colorPalette)];

            $backgroundColors[] = $color . '1A';
            $borderColors[] = $color;
        }

        return [
            'datasets' => [
                [
                    'label' => 'recipe category',
                    'data' => $data,
                    'backgroundColor' => $backgroundColors,
                    'borderColor' => $borderColors,
                    'borderWidth' => 1,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}
