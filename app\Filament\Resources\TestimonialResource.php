<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TestimonialResource\Pages;
use App\Filament\Resources\TestimonialResource\RelationManagers;
use App\Models\Testimonial;
use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Infolists\Components\Actions\Action;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Enums\ActionsPosition;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Mokhosh\FilamentRating\Columns\RatingColumn;
use Mokhosh\FilamentRating\Components\Rating;
use Mokhosh\FilamentRating\Entries\RatingEntry;
use Mokhosh\FilamentRating\RatingTheme;

class TestimonialResource extends Resource
{
    use Translatable;

    protected static ?string $model = Testimonial::class;
    protected static ?string $navigationIcon = 'heroicon-o-star';
    protected static ?string $navigationGroup = 'Customer Management';
    

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Detail Review')
                            ->schema([
                                Forms\Components\RichEditor::make('review_text')
                                    ->required()
                                    ->label('Isi Review')
                                    ->columnSpanFull(),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Informasi Customer')
                            ->schema([
                                Forms\Components\FileUpload::make('profile_image_url')
                                    ->label('Foto Profil')
                                    ->image()
                                    ->avatar()
                                    ->directory('review-profiles'),
                                Forms\Components\TextInput::make('customer_name')
                                    ->label('Nama Customer')
                                    ->required()
                                    ->maxLength(100),
                                Forms\Components\TextInput::make('customer_title')
                                    ->label('Jabatan/Title Customer')
                                    ->maxLength(100),
                            ]),

                        Forms\Components\Section::make('Atribut')
                            ->schema([
                                Rating::make('rating')
                                    ->label('Rating')
                                    ->stars(5)  
                                    ->theme(RatingTheme::HalfStars)
                                    ->allowZero(),

                                Forms\Components\Select::make('location_id')
                                    ->label('Lokasi')
                                    ->relationship('location', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->nullable(),
                                Forms\Components\Toggle::make('is_approved')
                                    ->label('Disetujui untuk Tampil')
                                    ->required()
                                    ->default(false),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('profile_image_url')
                    ->label('Foto')
                    ->circular(),
                TextColumn::make('customer_name')
                    ->label('Nama Customer')
                    ->searchable()
                    ->sortable(),
                // [3] Ganti komponen TextColumn dengan RatingColumn
                RatingColumn::make('rating')
                    ->label('Rating')
                    ->theme(RatingTheme::HalfStars)
                    ->sortable(),
                TextColumn::make('location.name')
                    ->label('Lokasi')
                    ->searchable()
                    ->sortable()
                    ->default('-'),
                ToggleColumn::make('is_approved')
                    ->label('Disetujui'),
                TextColumn::make('created_at')
                    ->label('Dibuat Pada')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->label('Diubah Pada')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TernaryFilter::make('is_approved')
                    ->label('Status Persetujuan')
                    ->placeholder('Semua')
                    ->trueLabel('Disetujui')
                    ->falseLabel('Belum Disetujui'),
                SelectFilter::make('location_id')
                    ->label('Lokasi')
                    ->relationship('location', 'name')
                    ->searchable()
                    ->preload(),
            Filter::make('rating')
                ->form([
                    TextInput::make('rating_from')
                        ->label('Rating Minimum')
                        ->numeric()
                        ->step(0.5)
                        ->minValue(0)
                        ->maxValue(5)
                        ->placeholder('Contoh: 3.5'),
                    TextInput::make('rating_to')
                        ->label('Rating Maksimum')
                        ->numeric()
                        ->step(0.5)
                        ->minValue(0)
                        ->maxValue(5)
                        ->placeholder('Contoh: 5'),
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query
                        ->when(
                            $data['rating_from'],
                            fn(Builder $query, $value): Builder => $query->where('rating', '>=', $value)
                        )
                        ->when(
                            $data['rating_to'],
                            fn(Builder $query, $value): Builder => $query->where('rating', '<=', $value)
                        );
                })
                ->indicateUsing(function (array $data): ?string {
                    if (! $data['rating_from'] && ! $data['rating_to']) {
                        return null;
                    }

                    if ($data['rating_from'] && $data['rating_to']) {
                        return 'Rating: ' . $data['rating_from'] . ' - ' . $data['rating_to'];
                    }

                    return $data['rating_from'] ? 'Rating dari ' . $data['rating_from'] : 'Rating hingga ' . $data['rating_to'];
                }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->iconButton(),
                Tables\Actions\EditAction::make()->iconButton(),
                Tables\Actions\DeleteAction::make()->iconButton(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([  
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Grid::make(3)->schema([
                    Grid::make()->columnSpan(2)->extraAttributes(['class' => 'bg-grid'])->schema([
                        Section::make('Ulasan Pelanggan')
                            ->schema([
                                TextEntry::make('customer_name')->size('xl')->label('Nama Pelanggan'),
                                TextEntry::make('customer_title')->size('xl')->label('Jabatan'),
                                TextEntry::make('review_text')->markdown()->columnSpanFull()->size('xl'),
                            ])->columns(2),
                    ]),
                    Grid::make()->columnSpan(1)->extraAttributes(['class' => 'bg-grid'])->schema([
                        Section::make('Info')
                            ->schema([
                                ImageEntry::make('profile_image_url')
                                    ->label('')
                                    ->circular()
                                    ->alignCenter(),
                                RatingEntry::make('rating')->theme(RatingTheme::HalfStars)->alignCenter(),
                                IconEntry::make('is_approved')
                                    ->label('Status')
                                    ->boolean()
                                    ->trueIcon('heroicon-o-check-badge')
                                    ->falseIcon('heroicon-o-x-circle')
                                    ->size('xl'),
                                TextEntry::make('location.name')->label('Lokasi')->size('xl'),
                            ]), 
                    ]),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTestimonials::route('/'),
            'create' => Pages\CreateTestimonial::route('/create'),
            // 'view' => Pages\ViewTestimonial::route('/{record}'),
            'edit' => Pages\EditTestimonial::route('/{record}/edit'),
        ];
    }
}
