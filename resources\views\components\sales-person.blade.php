@props([
    // Mendefinisikan properti 'people' yang diharapkan.
    // Defaultnya adalah array kosong untuk mencegah error jika tidak ada data yang dilewatkan.
    'people' => []
])


<!-- Awal: Kontainer Utama Komponen -->
<div class="bg-white font-sans py-12 sm:py-16 lg:py-20">
    <div class="container mx-auto px-4">

        <!-- Awal: Header (Judul dan Tombol Navigasi) -->
        <div class="flex justify-between items-center mb-8">
            {{-- Judul --}}
            <h2 class="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900">
                Best people that ready to <br> serve everyone
            </h2>

            {{-- Tombol Navigasi Carousel --}}
            <div class="flex items-center gap-3">
                <button aria-label="Previous" class="prev-btn h-12 w-12 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800 transition-colors disabled:opacity-50">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                <button aria-label="Next" class="next-btn h-12 w-12 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800 transition-colors disabled:opacity-50">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>
        </div>
        <!-- Akhir: Header -->

        <!-- Awal: Kontainer Carousel -->
        {{--
            'slider-container' adalah ID yang akan kita gunakan di JavaScript untuk mengontrol scroll.
            - 'flex': Membuat item di dalamnya berjajar horizontal.
            - 'overflow-x-auto': Memungkinkan scrolling horizontal.
            - 'snap-x snap-mandatory': Memberikan efek "snap" saat scrolling.
            - 'scroll-smooth': Animasi scrolling yang halus.
            - 'scrollbar-hide': Menyembunyikan scrollbar bawaan browser.
        --}}
        <div id="slider-container" class="flex overflow-x-auto snap-x snap-mandatory scroll-smooth scrollbar-hide -mx-2">

            @forelse ($people as $person)
                <!-- Awal: Kartu Profil (Looping) -->
                <div class="flex-none w-full sm:w-1/2 lg:w-1/3 xl:w-1/4 p-2 snap-start">
                    <div class="flex rounded-2xl overflow-hidden h-full shadow-lg border border-gray-200">

                        {{-- Bagian Kiri: Gambar --}}
                        <div class="relative w-1/2 bg-green-900">
                            <img src="{{ $person['imageUrl'] }}" alt="Foto {{ $person['name'] }}" class="w-full h-full object-cover">
                            {{-- Tombol Contact Us --}}
                            <a href="tel:{{ $person['phone'] }}" class="absolute bottom-4 left-1/2 -translate-x-1/2 w-4/5 bg-[#808264] text-white text-sm font-semibold py-2 px-3 rounded-full flex items-center justify-center gap-2 hover:bg-[#6a6c53] transition-colors">
                                Contact Us
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.518.76a11.024 11.024 0 006.25 6.25l.76-1.518a1 1 0 011.06-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                </svg>
                            </a>
                        </div>

                        {{-- Bagian Kanan: Informasi --}}
                        <div class="w-1/2 bg-[#F5F3EF] p-4 flex flex-col">
                            <h3 class="text-3xl font-bold text-[#6A6C53] font-serif">{{ $person['name'] }}</h3>
                            <p class="text-gray-500 text-sm mb-4">{{ $person['phone'] }}</p>

                            <div class="bg-[#808264] text-white text-center text-sm font-semibold py-1 px-3 rounded-md mb-4">
                                Distribute to
                            </div>

                            {{-- Daftar Distribusi --}}
                            <div class="space-y-3 text-xs overflow-y-auto flex-grow">
                                @foreach ($person['distributionPoints'] as $point)
                                    <div>
                                        <p class="font-bold text-gray-800">{{ $point['name'] }}</p>
                                        <p class="text-gray-600">{{ $point['address'] }}</p>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                    </div>
                </div>
                <!-- Akhir: Kartu Profil -->
            @empty
                {{-- Pesan jika tidak ada data --}}
                <p class="p-4 text-gray-500">No people to display.</p>
            @endforelse

        </div>
        <!-- Akhir: Kontainer Carousel -->
    </div>
</div>
<!-- Akhir: Kontainer Utama Komponen -->

{{--
=================================================================================================
| JavaScript untuk Kontrol Carousel                                                             |
|                                                                                               |
| Skrip ini menambahkan fungsionalitas pada tombol panah.                                       |
| Ini menggunakan JavaScript standar dan tidak memerlukan library eksternal seperti jQuery.     |
=================================================================================================
--}}
<script>
    // Menjalankan skrip setelah seluruh halaman dimuat
    document.addEventListener('DOMContentLoaded', function () {
        const slider = document.getElementById('slider-container');
        const nextButton = document.querySelector('.next-btn');
        const prevButton = document.querySelector('.prev-btn');

        if (!slider || !nextButton || !prevButton) {
            // Jika salah satu elemen tidak ditemukan, hentikan eksekusi
            console.warn('Slider elements not found.');
            return;
        }

        // Fungsi untuk memeriksa dan menonaktifkan tombol jika di awal atau akhir scroll
        const updateButtonState = () => {
            // Tombol 'prev' nonaktif jika scroll di paling kiri
            prevButton.disabled = slider.scrollLeft <= 0;
            // Tombol 'next' nonaktif jika scroll mencapai ujung kanan
            // 'scrollWidth' adalah lebar total konten, 'clientWidth' adalah lebar area yang terlihat
            nextButton.disabled = slider.scrollLeft + slider.clientWidth >= slider.scrollWidth - 1; // -1 untuk toleransi
        };

        // Event listener untuk tombol 'next'
        nextButton.addEventListener('click', () => {
            // Mengambil lebar satu kartu (item pertama di slider)
            const cardWidth = slider.querySelector('.flex-none').offsetWidth;
            // Geser ke kanan sejauh lebar satu kartu
            slider.scrollBy({ left: cardWidth, behavior: 'smooth' });
        });

        // Event listener untuk tombol 'prev'
        prevButton.addEventListener('click', () => {
            const cardWidth = slider.querySelector('.flex-none').offsetWidth;
            // Geser ke kiri sejauh lebar satu kartu
            slider.scrollBy({ left: -cardWidth, behavior: 'smooth' });
        });

        // Memperbarui status tombol setiap kali slider di-scroll
        slider.addEventListener('scroll', updateButtonState);

        // Memperbarui status tombol saat ukuran window berubah (untuk responsivitas)
        window.addEventListener('resize', updateButtonState);

        // Memanggil fungsi sekali saat halaman dimuat untuk mengatur status awal tombol
        updateButtonState();
    });
</script>