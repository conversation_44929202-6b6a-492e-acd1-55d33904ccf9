@extends('layouts.app')

@section('title', __('messages.title.catalog'))

@push('css')
    <style>
        .no-scrollbar::-webkit-scrollbar {
            display: none;
        }
    
        .no-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
    </style>
@endpush

@section('content')
<div class="w-full mb-12">
    <div class="aspect-w-16 aspect-h-9 h-screen mx-auto relative">
            <div id="banner-container" class="grid grid-flow-col grid-rows-1 gap-3 overflow-x-auto snap-x snap-mandatory scroll-smooth no-scrollbar">
                @foreach ($products as $product)
                <div class="snap-center w-screen h-screen flex-shrink-0">
                    <div class="bg-gray-500 p-2 text-center flex flex-col justify-between h-full">
                        <img src="{{asset('storage/'.$product->image_url) }}" alt="{{ $product->name }} Product"
                            class="mx-auto mt-1 max-h-48 object-contain" loading="lazy">
                    </div>
                </div>
                @endforeach
            </div>
            <button id="prev-btn-banner"
                class="absolute left-0 top-1/2 -translate-y-1/2 bg-transparent rounded-full p-3 ml-2 shadow-lg z-10 hover:scale-105 transition-transform duration-200">
                <svg class="w-6 h-6 text-brand-green" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                    stroke-width="2" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                </svg>
            </button>
            <button id="next-btn-banner"
                class="absolute right-0 top-1/2 -translate-y-1/2 bg-transparent rounded-full p-3 mr-2 shadow-lg z-10 hover:scale-105 transition-transform duration-200">
                <svg class="w-6 h-6 text-brand-green" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                    stroke-width="2" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                </svg>
            </button>
    </div>
</div>

<div class="bg-slate-200">
    <main class="text-brand-dark-text">
        <section class="py-16">
            <div class="container mx-auto px-3 relative">

                <!--* Kontainer untuk grid produk yang bisa di-scroll -->
                <div id="product-container" class="grid grid-flow-col grid-rows-2 gap-3 overflow-x-auto snap-x snap-mandatory scroll-smooth no-scrollbar py-4">

                    <!--* card setiap produk -->
                    @foreach ($products as $product)
                    <div class="snap-center w-[95vw] lg:w-[45vw] flex-shrink-0 ">
                        <div class="bg-brand-beige rounded-xl shadow-md p-2 text-center flex flex-col justify-between h-full">
                            <img src="{{asset('storage/'.$product->image_url) }}" alt="{{ $product->name }} Product" class="mx-auto mt-1 max-h-48 object-contain" loading="lazy">
                            <div>
                                <h3 class="text-2xl font-semibold mt-4">{{ $product->name }}</h3>
                                <div class="grid grid-cols-3 gap-4 mt-4 text-sm">
                                    <div>
                                        <p>Flavor</p>
                                        <p class="font-semibold">{{ $product->flavor }}</p>
                                    </div>
                                    <div>
                                        <p>Color</p>
                                        <p class="font-semibold">{{ $product->color }}</p>
                                    </div>
                                    <div>
                                        <p>Pack Size</p>
                                        <p class="font-semibold">{{ $product->pack_size }}</p>
                                    </div>
                                </div>
                                <a href="{{ route('list-catalog-detail', ['slug' => $product->slug]) }}">
                                    Lihat Detail
                                </a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
        
                <!-- * Tombol Navigasi -->
                <button id="prev-btn" class="absolute left-0 top-1/2 -translate-y-1/2 bg-transparent rounded-full p-3 ml-2 shadow-lg z-10 hover:scale-105 transition-transform duration-200">
                    <svg class="w-6 h-6 text-brand-green" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke-width="2" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                    </svg>
                </button>
                <button id="next-btn" class="absolute right-0 top-1/2 -translate-y-1/2 bg-transparent rounded-full p-3 mr-2 shadow-lg z-10 hover:scale-105 transition-transform duration-200">
                    <svg class="w-6 h-6 text-brand-green" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke-width="2" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                    </svg>
                </button>
            </div>
        </section>

        <section class="container mx-auto px-4 py-16">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
                <div class="relative">
                    <img src="https://i.imgur.com/sWjXQkC.png" alt="Palm fruit harvest"
                        class="rounded-lg w-full h-auto">
                    <button
                        class="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 bg-white rounded-full p-3 shadow-lg">
                        <svg class="w-6 h-6 text-brand-green" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                        </svg>
                    </button>
                </div>
                <div>
                    <h2 class="text-3xl font-bold mb-4">Lorem ipsum dolor sit amet, consectetuer.</h2>
                    <p class="text-gray-600 leading-relaxed">
                        Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod
                        tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis
                        nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat.
                    </p>
                </div>
            </div>
        </section>
    </main>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function () {
        /**
         * Fungsi reusable untuk menginisialisasi slider.
         * @param {string} containerId - ID dari kontainer slider.
         * @param {string} prevBtnId - ID dari tombol 'previous'.
         * @param {string} nextBtnId - ID dari tombol 'next'.
         * @param {object} options - Opsi tambahan untuk slider.
         * @param {boolean} options.autoSlide - Apakah slider harus bergeser otomatis.
         * @param {number} options.interval - Interval waktu untuk auto-slide (dalam ms).
         */
        function initializeSlider(containerId, prevBtnId, nextBtnId, options = {}) {
            const container = document.getElementById(containerId);
            const prevBtn = document.getElementById(prevBtnId);
            const nextBtn = document.getElementById(nextBtnId);

            if (!container || !prevBtn || !nextBtn) {
                console.warn(`Slider with container #${containerId} could not be initialized.`);
                return;
            }

            let autoSlideInterval = null;
            const autoSlideEnabled = options.autoSlide || false;
            const slideInterval = options.interval || 2000;

            const scroll = (direction) => {
                const scrollAmount = container.clientWidth * direction;
                container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
            };

            const slideNext = () => {
                const { scrollWidth, clientWidth, scrollLeft } = container;
                if (scrollLeft + clientWidth >= scrollWidth - 1) {
                    container.scrollTo({ left: 0, behavior: 'smooth' });
                } else {
                    scroll(1);
                }
            };

            const startAutoSlide = () => {
                if (autoSlideInterval === null) {
                    autoSlideInterval = setInterval(slideNext, slideInterval);
                }
            };

            const stopAutoSlide = () => {
                clearInterval(autoSlideInterval);
                autoSlideInterval = null;
            };

            prevBtn.addEventListener('click', () => {
                scroll(-1);
                if (autoSlideEnabled) {
                    stopAutoSlide();
                    startAutoSlide();
                }
            });

            nextBtn.addEventListener('click', () => {
                scroll(1);
                if (autoSlideEnabled) {
                    stopAutoSlide();
                    startAutoSlide();
                }
            });

            // Logika untuk auto-slide jika diaktifkan
            if (autoSlideEnabled) {
                container.addEventListener('mouseenter', stopAutoSlide);
                container.addEventListener('mouseleave', startAutoSlide);

                // 💡 Buat Intersection Observer
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            startAutoSlide();
                        } else {
                            stopAutoSlide();
                        }
                    });
                }, {
                    rootMargin: '0px',
                    threshold: 0.1 
                });

                observer.observe(container);
            }
        }

        // --- Inisialisasi Slider ---

        // 1. Inisialisasi slider banner dengan auto-slide
        initializeSlider('banner-container', 'prev-btn-banner', 'next-btn-banner', {
            autoSlide: true,
            interval: 10000
        });

        // 2. Inisialisasi slider produk (tanpa auto-slide)
        initializeSlider('product-container', 'prev-btn', 'next-btn');

    });
</script>
@endpush
